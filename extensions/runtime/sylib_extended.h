#ifndef SYLIB_EXTENDED_H
#define SYLIB_EXTENDED_H

#include <stdio.h>
#include <stdarg.h>
#include <sys/time.h>
#include <pthread.h>
#include <stdlib.h>

// Original SysY runtime functions
int getint();
int getch();
float getfloat();
int getarray(int a[]);
int getfarray(float a[]);
void putint(int a);
void putch(int a);
void putfloat(float a);
void putarray(int n, int a[]);
void putfarray(int n, float a[]);
void putf(char a[], ...);
struct timeval _sysy_start, _sysy_end;
#define starttime() _sysy_starttime(__LINE__)
#define stoptime() _sysy_stoptime(__LINE__)
void _sysy_starttime(int lineno);
void _sysy_stoptime(int lineno);

// Extended runtime functions for parallel execution
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int total_threads;
    int generation;
} barrier_t;

// Barrier functions
void barrier_init(barrier_t* barrier, int num_threads);
void barrier_wait(barrier_t* barrier);
void barrier_destroy(barrier_t* barrier);

// Thread management for parallel for loops
typedef struct {
    int thread_id;
    int start_idx;
    int end_idx;
    int step;
    void* loop_data;
    void (*loop_body)(int, void*);
    barrier_t* barrier;
} thread_data_t;

// Parallel for loop execution
void parallel_for(int start, int end, int step, int num_threads, 
                  void (*loop_body)(int, void*), void* data);

// Global barrier for simple barrier statements
extern barrier_t global_barrier;
extern int global_thread_count;

// Simple barrier function for barrier statements
void sysy_barrier();
void sysy_set_thread_count(int count);

#endif // SYLIB_EXTENDED_H
