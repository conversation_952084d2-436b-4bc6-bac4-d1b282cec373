#include "sylib_extended.h"

// Original SysY runtime functions implementation
int getint() {
    int t;
    scanf("%d", &t);
    return t;
}

int getch() {
    char c;
    scanf("%c", &c);
    return (int)c;
}

float getfloat() {
    float n;
    scanf("%a", &n);
    return n;
}

int getarray(int a[]) {
    int n;
    scanf("%d", &n);
    for (int i = 0; i < n; i++) {
        scanf("%d", &a[i]);
    }
    return n;
}

int getfarray(float a[]) {
    int n;
    scanf("%d", &n);
    for (int i = 0; i < n; i++) {
        scanf("%a", &a[i]);
    }
    return n;
}

void putint(int a) {
    printf("%d", a);
}

void putch(int a) {
    printf("%c", a);
}

void putfloat(float a) {
    printf("%a", a);
}

void putarray(int n, int a[]) {
    printf("%d:", n);
    for (int i = 0; i < n; i++) {
        printf(" %d", a[i]);
    }
    printf("\n");
}

void putfarray(int n, float a[]) {
    printf("%d:", n);
    for (int i = 0; i < n; i++) {
        printf(" %a", a[i]);
    }
    printf("\n");
}

void putf(char a[], ...) {
    va_list args;
    va_start(args, a);
    vprintf(a, args);
    va_end(args);
}

void _sysy_starttime(int lineno) {
    gettimeofday(&_sysy_start, NULL);
}

void _sysy_stoptime(int lineno) {
    gettimeofday(&_sysy_end, NULL);
    printf("Timer@%04d-%04d: %ldus\n", lineno, lineno,
           (_sysy_end.tv_sec - _sysy_start.tv_sec) * 1000000 + 
           (_sysy_end.tv_usec - _sysy_start.tv_usec));
}

// Extended runtime functions for parallel execution

// Barrier implementation
void barrier_init(barrier_t* barrier, int num_threads) {
    pthread_mutex_init(&barrier->mutex, NULL);
    pthread_cond_init(&barrier->cond, NULL);
    barrier->count = 0;
    barrier->total_threads = num_threads;
    barrier->generation = 0;
}

void barrier_wait(barrier_t* barrier) {
    pthread_mutex_lock(&barrier->mutex);
    
    int gen = barrier->generation;
    barrier->count++;
    
    if (barrier->count == barrier->total_threads) {
        // Last thread to arrive
        barrier->count = 0;
        barrier->generation++;
        pthread_cond_broadcast(&barrier->cond);
    } else {
        // Wait for all threads to arrive
        while (gen == barrier->generation) {
            pthread_cond_wait(&barrier->cond, &barrier->mutex);
        }
    }
    
    pthread_mutex_unlock(&barrier->mutex);
}

void barrier_destroy(barrier_t* barrier) {
    pthread_mutex_destroy(&barrier->mutex);
    pthread_cond_destroy(&barrier->cond);
}

// Thread function for parallel for loops
void* thread_function(void* arg) {
    thread_data_t* data = (thread_data_t*)arg;
    
    for (int i = data->start_idx; i < data->end_idx; i += data->step) {
        data->loop_body(i, data->loop_data);
    }
    
    if (data->barrier) {
        barrier_wait(data->barrier);
    }
    
    return NULL;
}

// Parallel for loop execution
void parallel_for(int start, int end, int step, int num_threads, 
                  void (*loop_body)(int, void*), void* data) {
    if (num_threads <= 1) {
        // Sequential execution
        for (int i = start; i < end; i += step) {
            loop_body(i, data);
        }
        return;
    }
    
    pthread_t* threads = malloc(num_threads * sizeof(pthread_t));
    thread_data_t* thread_data = malloc(num_threads * sizeof(thread_data_t));
    barrier_t barrier;
    
    barrier_init(&barrier, num_threads);
    
    int range = (end - start + step - 1) / step;  // Total iterations
    int chunk_size = range / num_threads;
    int remainder = range % num_threads;
    
    int current_start = start;
    
    for (int i = 0; i < num_threads; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].start_idx = current_start;
        
        int iterations = chunk_size + (i < remainder ? 1 : 0);
        thread_data[i].end_idx = current_start + iterations * step;
        thread_data[i].step = step;
        thread_data[i].loop_data = data;
        thread_data[i].loop_body = loop_body;
        thread_data[i].barrier = &barrier;
        
        pthread_create(&threads[i], NULL, thread_function, &thread_data[i]);
        
        current_start = thread_data[i].end_idx;
    }
    
    // Wait for all threads to complete
    for (int i = 0; i < num_threads; i++) {
        pthread_join(threads[i], NULL);
    }
    
    barrier_destroy(&barrier);
    free(threads);
    free(thread_data);
}

// Global barrier for simple barrier statements
barrier_t global_barrier;
int global_thread_count = 1;
static int global_barrier_initialized = 0;

void sysy_set_thread_count(int count) {
    global_thread_count = count;
    if (global_barrier_initialized) {
        barrier_destroy(&global_barrier);
    }
    barrier_init(&global_barrier, count);
    global_barrier_initialized = 1;
}

void sysy_barrier() {
    if (!global_barrier_initialized) {
        barrier_init(&global_barrier, global_thread_count);
        global_barrier_initialized = 1;
    }
    barrier_wait(&global_barrier);
}
