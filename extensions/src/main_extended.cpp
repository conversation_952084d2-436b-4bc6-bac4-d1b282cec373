#include <iostream>

#include "antlr4-runtime.h"
#include "SysYExtendedLexer.h"
#include "SysYExtendedParser.h"
#include "ASTVisitorExtended.hpp"

using namespace antlr4;

int main(int argc, const char* argv[]) {
    std::ifstream stream;
    stream.open(argv[1]);
    ANTLRInputStream input(stream);
    //ANTLRInputStream input(std::cin);

    // Lexer
    SysYExtendedLexer lexer(&input);
    CommonTokenStream tokens(&lexer);

    tokens.fill();
   
    for (auto token : tokens.getTokens()) {
  
        //简单粗暴的输出token信息并不符合题目要求
        std::cout << token->toString() << std::endl;
    }

    /* 语法分析
    sysyparser parser(&tokens);
    tree::parsetree* tree = parser.compunit();

    std::cout << tree->tostringtree(&parser) << std::endl << std::endl;
    */
    SysYExtendedParser parser(&tokens);
    tree::ParseTree* tree = parser.program();

    std::cout << tree->toStringTree(&parser) << std::endl << std::endl;


    std::cout << "=== AST Structure ===" << std::endl;
    ASTVisitorExtended visitor;
    visitor.visit(tree);

    // Print unoptimized LLVM IR
    std::cout << "\n=== Unoptimized LLVM IR ===" << std::endl;
    visitor.print_llvm_ir();

    // Run optimization passes (including mem2reg)
    visitor.optimize_ir();

    // Print optimized LLVM IR
    std::cout << "\n=== Optimized LLVM IR (after mem2reg) ===" << std::endl;
    visitor.print_llvm_ir();

    return 0;
}
