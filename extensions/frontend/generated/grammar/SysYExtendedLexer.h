
// Generated from grammar/SysYExtended.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"




class  SysYExtendedLexer : public antlr4::Lexer {
public:
  enum {
    INT = 1, FLOAT = 2, VOID = 3, CONST = 4, IF = 5, ELSE = 6, WHILE = 7, 
    FOR = 8, BREAK = 9, CONTINUE = 10, RETURN = 11, PRAGMA = 12, PARALLEL = 13, 
    BARRIER = 14, SEMICOLON = 15, COMMA = 16, LPAREN = 17, RPAREN = 18, 
    LBRACE = 19, RBRACE = 20, LBRACKET = 21, RBRACKET = 22, ASSIGN = 23, 
    ADD = 24, SUB = 25, MUL = 26, DIV = 27, MOD = 28, LT = 29, GT = 30, 
    LE = 31, GE = 32, EQ = 33, NE = 34, AND = 35, OR = 36, NOT = 37, IDENT = 38, 
    WHIESPACE = 39, LINECOMMENT = 40, BLOCKCOMMENT = 41, INTCONST = 42, 
    FLOATCONST = 43
  };

  SysYExtendedLexer(antlr4::CharStream *input);
  ~SysYExtendedLexer();

  virtual std::string getGrammarFileName() const override;
  virtual const std::vector<std::string>& getRuleNames() const override;

  virtual const std::vector<std::string>& getChannelNames() const override;
  virtual const std::vector<std::string>& getModeNames() const override;
  virtual const std::vector<std::string>& getTokenNames() const override; // deprecated, use vocabulary instead
  virtual antlr4::dfa::Vocabulary& getVocabulary() const override;

  virtual const std::vector<uint16_t> getSerializedATN() const override;
  virtual const antlr4::atn::ATN& getATN() const override;

private:
  static std::vector<antlr4::dfa::DFA> _decisionToDFA;
  static antlr4::atn::PredictionContextCache _sharedContextCache;
  static std::vector<std::string> _ruleNames;
  static std::vector<std::string> _tokenNames;
  static std::vector<std::string> _channelNames;
  static std::vector<std::string> _modeNames;

  static std::vector<std::string> _literalNames;
  static std::vector<std::string> _symbolicNames;
  static antlr4::dfa::Vocabulary _vocabulary;
  static antlr4::atn::ATN _atn;
  static std::vector<uint16_t> _serializedATN;


  // Individual action functions triggered by action() above.

  // Individual semantic predicate functions triggered by sempred() above.

  struct Initializer {
    Initializer();
  };
  static Initializer _init;
};

