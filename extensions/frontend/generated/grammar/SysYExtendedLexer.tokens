INT=1
FLOAT=2
VOID=3
CONST=4
IF=5
ELSE=6
WHILE=7
FOR=8
BREAK=9
CONTINUE=10
RETURN=11
PRAGMA=12
PARALLEL=13
BARRIER=14
SEMICOLON=15
COMMA=16
LPAREN=17
RPAREN=18
LBRACE=19
RBRACE=20
LBRACKET=21
RBRACKET=22
ASSIGN=23
ADD=24
SUB=25
MUL=26
DIV=27
MOD=28
LT=29
GT=30
LE=31
GE=32
EQ=33
NE=34
AND=35
OR=36
NOT=37
IDENT=38
WHIESPACE=39
LINECOMMENT=40
BLOCKCOMMENT=41
INTCONST=42
FLOATCONST=43
'int'=1
'float'=2
'void'=3
'const'=4
'if'=5
'else'=6
'while'=7
'for'=8
'break'=9
'continue'=10
'return'=11
'#pragma'=12
'parallel'=13
'barrier'=14
';'=15
','=16
'('=17
')'=18
'{'=19
'}'=20
'['=21
']'=22
'='=23
'+'=24
'-'=25
'*'=26
'/'=27
'%'=28
'<'=29
'>'=30
'<='=31
'>='=32
'=='=33
'!='=34
'&&'=35
'||'=36
'!'=37
