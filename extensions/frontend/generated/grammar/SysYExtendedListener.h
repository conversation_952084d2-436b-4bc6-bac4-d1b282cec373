
// Generated from grammar/SysYExtended.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"
#include "SysYExtendedParser.h"


/**
 * This interface defines an abstract listener for a parse tree produced by SysYExtendedParser.
 */
class  SysYExtendedListener : public antlr4::tree::ParseTreeListener {
public:

  virtual void enterProgram(SysYExtendedParser::ProgramContext *ctx) = 0;
  virtual void exitProgram(SysYExtendedParser::ProgramContext *ctx) = 0;

  virtual void enterCompUnit(SysYExtendedParser::CompUnitContext *ctx) = 0;
  virtual void exitCompUnit(SysYExtendedParser::CompUnitContext *ctx) = 0;

  virtual void enterDecl(SysYExtendedParser::DeclContext *ctx) = 0;
  virtual void exitDecl(SysYExtendedParser::DeclContext *ctx) = 0;

  virtual void enterConstDecl(SysYExtendedParser::ConstDeclContext *ctx) = 0;
  virtual void exitConstDecl(SysYExtendedParser::ConstDeclContext *ctx) = 0;

  virtual void enterBType(SysYExtendedParser::BTypeContext *ctx) = 0;
  virtual void exitBType(SysYExtendedParser::BTypeContext *ctx) = 0;

  virtual void enterConstDef(SysYExtendedParser::ConstDefContext *ctx) = 0;
  virtual void exitConstDef(SysYExtendedParser::ConstDefContext *ctx) = 0;

  virtual void enterConstInitVal(SysYExtendedParser::ConstInitValContext *ctx) = 0;
  virtual void exitConstInitVal(SysYExtendedParser::ConstInitValContext *ctx) = 0;

  virtual void enterVarDecl(SysYExtendedParser::VarDeclContext *ctx) = 0;
  virtual void exitVarDecl(SysYExtendedParser::VarDeclContext *ctx) = 0;

  virtual void enterVarDef(SysYExtendedParser::VarDefContext *ctx) = 0;
  virtual void exitVarDef(SysYExtendedParser::VarDefContext *ctx) = 0;

  virtual void enterInitVal(SysYExtendedParser::InitValContext *ctx) = 0;
  virtual void exitInitVal(SysYExtendedParser::InitValContext *ctx) = 0;

  virtual void enterFuncDef(SysYExtendedParser::FuncDefContext *ctx) = 0;
  virtual void exitFuncDef(SysYExtendedParser::FuncDefContext *ctx) = 0;

  virtual void enterFuncType(SysYExtendedParser::FuncTypeContext *ctx) = 0;
  virtual void exitFuncType(SysYExtendedParser::FuncTypeContext *ctx) = 0;

  virtual void enterFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext *ctx) = 0;
  virtual void exitFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext *ctx) = 0;

  virtual void enterFuncFormalParam(SysYExtendedParser::FuncFormalParamContext *ctx) = 0;
  virtual void exitFuncFormalParam(SysYExtendedParser::FuncFormalParamContext *ctx) = 0;

  virtual void enterBlock(SysYExtendedParser::BlockContext *ctx) = 0;
  virtual void exitBlock(SysYExtendedParser::BlockContext *ctx) = 0;

  virtual void enterBlockItem(SysYExtendedParser::BlockItemContext *ctx) = 0;
  virtual void exitBlockItem(SysYExtendedParser::BlockItemContext *ctx) = 0;

  virtual void enterStmt(SysYExtendedParser::StmtContext *ctx) = 0;
  virtual void exitStmt(SysYExtendedParser::StmtContext *ctx) = 0;

  virtual void enterExp(SysYExtendedParser::ExpContext *ctx) = 0;
  virtual void exitExp(SysYExtendedParser::ExpContext *ctx) = 0;

  virtual void enterCond(SysYExtendedParser::CondContext *ctx) = 0;
  virtual void exitCond(SysYExtendedParser::CondContext *ctx) = 0;

  virtual void enterLVal(SysYExtendedParser::LValContext *ctx) = 0;
  virtual void exitLVal(SysYExtendedParser::LValContext *ctx) = 0;

  virtual void enterPrimaryExp(SysYExtendedParser::PrimaryExpContext *ctx) = 0;
  virtual void exitPrimaryExp(SysYExtendedParser::PrimaryExpContext *ctx) = 0;

  virtual void enterNumber(SysYExtendedParser::NumberContext *ctx) = 0;
  virtual void exitNumber(SysYExtendedParser::NumberContext *ctx) = 0;

  virtual void enterUnaryExp(SysYExtendedParser::UnaryExpContext *ctx) = 0;
  virtual void exitUnaryExp(SysYExtendedParser::UnaryExpContext *ctx) = 0;

  virtual void enterUnaryOP(SysYExtendedParser::UnaryOPContext *ctx) = 0;
  virtual void exitUnaryOP(SysYExtendedParser::UnaryOPContext *ctx) = 0;

  virtual void enterFuncRealParams(SysYExtendedParser::FuncRealParamsContext *ctx) = 0;
  virtual void exitFuncRealParams(SysYExtendedParser::FuncRealParamsContext *ctx) = 0;

  virtual void enterMulExp(SysYExtendedParser::MulExpContext *ctx) = 0;
  virtual void exitMulExp(SysYExtendedParser::MulExpContext *ctx) = 0;

  virtual void enterAddExp(SysYExtendedParser::AddExpContext *ctx) = 0;
  virtual void exitAddExp(SysYExtendedParser::AddExpContext *ctx) = 0;

  virtual void enterRelExp(SysYExtendedParser::RelExpContext *ctx) = 0;
  virtual void exitRelExp(SysYExtendedParser::RelExpContext *ctx) = 0;

  virtual void enterEqExp(SysYExtendedParser::EqExpContext *ctx) = 0;
  virtual void exitEqExp(SysYExtendedParser::EqExpContext *ctx) = 0;

  virtual void enterLAndExp(SysYExtendedParser::LAndExpContext *ctx) = 0;
  virtual void exitLAndExp(SysYExtendedParser::LAndExpContext *ctx) = 0;

  virtual void enterLOrExp(SysYExtendedParser::LOrExpContext *ctx) = 0;
  virtual void exitLOrExp(SysYExtendedParser::LOrExpContext *ctx) = 0;

  virtual void enterConstExp(SysYExtendedParser::ConstExpContext *ctx) = 0;
  virtual void exitConstExp(SysYExtendedParser::ConstExpContext *ctx) = 0;

  virtual void enterForStmt(SysYExtendedParser::ForStmtContext *ctx) = 0;
  virtual void exitForStmt(SysYExtendedParser::ForStmtContext *ctx) = 0;

  virtual void enterForInit(SysYExtendedParser::ForInitContext *ctx) = 0;
  virtual void exitForInit(SysYExtendedParser::ForInitContext *ctx) = 0;

  virtual void enterForUpdate(SysYExtendedParser::ForUpdateContext *ctx) = 0;
  virtual void exitForUpdate(SysYExtendedParser::ForUpdateContext *ctx) = 0;

  virtual void enterParallelForStmt(SysYExtendedParser::ParallelForStmtContext *ctx) = 0;
  virtual void exitParallelForStmt(SysYExtendedParser::ParallelForStmtContext *ctx) = 0;

  virtual void enterBarrierStmt(SysYExtendedParser::BarrierStmtContext *ctx) = 0;
  virtual void exitBarrierStmt(SysYExtendedParser::BarrierStmtContext *ctx) = 0;


};

