
// Generated from grammar/SysYExtended.g4 by ANTLR 4.7.2


#include "SysYExtendedListener.h"
#include "SysYExtendedVisitor.h"

#include "SysYExtendedParser.h"


using namespace antlrcpp;
using namespace antlr4;

SysYExtendedParser::SysYExtendedParser(TokenStream *input) : Parser(input) {
  _interpreter = new atn::ParserATNSimulator(this, _atn, _decisionToDFA, _sharedContextCache);
}

SysYExtendedParser::~SysYExtendedParser() {
  delete _interpreter;
}

std::string SysYExtendedParser::getGrammarFileName() const {
  return "SysYExtended.g4";
}

const std::vector<std::string>& SysYExtendedParser::getRuleNames() const {
  return _ruleNames;
}

dfa::Vocabulary& SysYExtendedParser::getVocabulary() const {
  return _vocabulary;
}


//----------------- ProgramContext ------------------------------------------------------------------

SysYExtendedParser::ProgramContext::ProgramContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::ProgramContext::EOF() {
  return getToken(SysYExtendedParser::EOF, 0);
}

std::vector<SysYExtendedParser::CompUnitContext *> SysYExtendedParser::ProgramContext::compUnit() {
  return getRuleContexts<SysYExtendedParser::CompUnitContext>();
}

SysYExtendedParser::CompUnitContext* SysYExtendedParser::ProgramContext::compUnit(size_t i) {
  return getRuleContext<SysYExtendedParser::CompUnitContext>(i);
}


size_t SysYExtendedParser::ProgramContext::getRuleIndex() const {
  return SysYExtendedParser::RuleProgram;
}

void SysYExtendedParser::ProgramContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterProgram(this);
}

void SysYExtendedParser::ProgramContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitProgram(this);
}


antlrcpp::Any SysYExtendedParser::ProgramContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitProgram(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ProgramContext* SysYExtendedParser::program() {
  ProgramContext *_localctx = _tracker.createInstance<ProgramContext>(_ctx, getState());
  enterRule(_localctx, 0, SysYExtendedParser::RuleProgram);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(75); 
    _errHandler->sync(this);
    _la = _input->LA(1);
    do {
      setState(74);
      compUnit();
      setState(77); 
      _errHandler->sync(this);
      _la = _input->LA(1);
    } while ((((_la & ~ 0x3fULL) == 0) &&
      ((1ULL << _la) & ((1ULL << SysYExtendedParser::INT)
      | (1ULL << SysYExtendedParser::FLOAT)
      | (1ULL << SysYExtendedParser::VOID)
      | (1ULL << SysYExtendedParser::CONST))) != 0));
    setState(79);
    match(SysYExtendedParser::EOF);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- CompUnitContext ------------------------------------------------------------------

SysYExtendedParser::CompUnitContext::CompUnitContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::DeclContext* SysYExtendedParser::CompUnitContext::decl() {
  return getRuleContext<SysYExtendedParser::DeclContext>(0);
}

SysYExtendedParser::FuncDefContext* SysYExtendedParser::CompUnitContext::funcDef() {
  return getRuleContext<SysYExtendedParser::FuncDefContext>(0);
}


size_t SysYExtendedParser::CompUnitContext::getRuleIndex() const {
  return SysYExtendedParser::RuleCompUnit;
}

void SysYExtendedParser::CompUnitContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterCompUnit(this);
}

void SysYExtendedParser::CompUnitContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitCompUnit(this);
}


antlrcpp::Any SysYExtendedParser::CompUnitContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitCompUnit(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::CompUnitContext* SysYExtendedParser::compUnit() {
  CompUnitContext *_localctx = _tracker.createInstance<CompUnitContext>(_ctx, getState());
  enterRule(_localctx, 2, SysYExtendedParser::RuleCompUnit);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(83);
    _errHandler->sync(this);
    switch (getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 1, _ctx)) {
    case 1: {
      enterOuterAlt(_localctx, 1);
      setState(81);
      decl();
      break;
    }

    case 2: {
      enterOuterAlt(_localctx, 2);
      setState(82);
      funcDef();
      break;
    }

    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- DeclContext ------------------------------------------------------------------

SysYExtendedParser::DeclContext::DeclContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::ConstDeclContext* SysYExtendedParser::DeclContext::constDecl() {
  return getRuleContext<SysYExtendedParser::ConstDeclContext>(0);
}

SysYExtendedParser::VarDeclContext* SysYExtendedParser::DeclContext::varDecl() {
  return getRuleContext<SysYExtendedParser::VarDeclContext>(0);
}


size_t SysYExtendedParser::DeclContext::getRuleIndex() const {
  return SysYExtendedParser::RuleDecl;
}

void SysYExtendedParser::DeclContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterDecl(this);
}

void SysYExtendedParser::DeclContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitDecl(this);
}


antlrcpp::Any SysYExtendedParser::DeclContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitDecl(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::DeclContext* SysYExtendedParser::decl() {
  DeclContext *_localctx = _tracker.createInstance<DeclContext>(_ctx, getState());
  enterRule(_localctx, 4, SysYExtendedParser::RuleDecl);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(87);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::CONST: {
        enterOuterAlt(_localctx, 1);
        setState(85);
        constDecl();
        break;
      }

      case SysYExtendedParser::INT:
      case SysYExtendedParser::FLOAT: {
        enterOuterAlt(_localctx, 2);
        setState(86);
        varDecl();
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ConstDeclContext ------------------------------------------------------------------

SysYExtendedParser::ConstDeclContext::ConstDeclContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::ConstDeclContext::CONST() {
  return getToken(SysYExtendedParser::CONST, 0);
}

SysYExtendedParser::BTypeContext* SysYExtendedParser::ConstDeclContext::bType() {
  return getRuleContext<SysYExtendedParser::BTypeContext>(0);
}

std::vector<SysYExtendedParser::ConstDefContext *> SysYExtendedParser::ConstDeclContext::constDef() {
  return getRuleContexts<SysYExtendedParser::ConstDefContext>();
}

SysYExtendedParser::ConstDefContext* SysYExtendedParser::ConstDeclContext::constDef(size_t i) {
  return getRuleContext<SysYExtendedParser::ConstDefContext>(i);
}

tree::TerminalNode* SysYExtendedParser::ConstDeclContext::SEMICOLON() {
  return getToken(SysYExtendedParser::SEMICOLON, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ConstDeclContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::ConstDeclContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::ConstDeclContext::getRuleIndex() const {
  return SysYExtendedParser::RuleConstDecl;
}

void SysYExtendedParser::ConstDeclContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterConstDecl(this);
}

void SysYExtendedParser::ConstDeclContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitConstDecl(this);
}


antlrcpp::Any SysYExtendedParser::ConstDeclContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitConstDecl(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ConstDeclContext* SysYExtendedParser::constDecl() {
  ConstDeclContext *_localctx = _tracker.createInstance<ConstDeclContext>(_ctx, getState());
  enterRule(_localctx, 6, SysYExtendedParser::RuleConstDecl);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(89);
    match(SysYExtendedParser::CONST);
    setState(90);
    bType();
    setState(91);
    constDef();
    setState(96);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while (_la == SysYExtendedParser::COMMA) {
      setState(92);
      match(SysYExtendedParser::COMMA);
      setState(93);
      constDef();
      setState(98);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
    setState(99);
    match(SysYExtendedParser::SEMICOLON);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- BTypeContext ------------------------------------------------------------------

SysYExtendedParser::BTypeContext::BTypeContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::BTypeContext::INT() {
  return getToken(SysYExtendedParser::INT, 0);
}

tree::TerminalNode* SysYExtendedParser::BTypeContext::FLOAT() {
  return getToken(SysYExtendedParser::FLOAT, 0);
}


size_t SysYExtendedParser::BTypeContext::getRuleIndex() const {
  return SysYExtendedParser::RuleBType;
}

void SysYExtendedParser::BTypeContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterBType(this);
}

void SysYExtendedParser::BTypeContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitBType(this);
}


antlrcpp::Any SysYExtendedParser::BTypeContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitBType(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::BTypeContext* SysYExtendedParser::bType() {
  BTypeContext *_localctx = _tracker.createInstance<BTypeContext>(_ctx, getState());
  enterRule(_localctx, 8, SysYExtendedParser::RuleBType);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(101);
    _la = _input->LA(1);
    if (!(_la == SysYExtendedParser::INT

    || _la == SysYExtendedParser::FLOAT)) {
    _errHandler->recoverInline(this);
    }
    else {
      _errHandler->reportMatch(this);
      consume();
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ConstDefContext ------------------------------------------------------------------

SysYExtendedParser::ConstDefContext::ConstDefContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::ConstDefContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

tree::TerminalNode* SysYExtendedParser::ConstDefContext::ASSIGN() {
  return getToken(SysYExtendedParser::ASSIGN, 0);
}

SysYExtendedParser::ConstInitValContext* SysYExtendedParser::ConstDefContext::constInitVal() {
  return getRuleContext<SysYExtendedParser::ConstInitValContext>(0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ConstDefContext::LBRACKET() {
  return getTokens(SysYExtendedParser::LBRACKET);
}

tree::TerminalNode* SysYExtendedParser::ConstDefContext::LBRACKET(size_t i) {
  return getToken(SysYExtendedParser::LBRACKET, i);
}

std::vector<SysYExtendedParser::ConstExpContext *> SysYExtendedParser::ConstDefContext::constExp() {
  return getRuleContexts<SysYExtendedParser::ConstExpContext>();
}

SysYExtendedParser::ConstExpContext* SysYExtendedParser::ConstDefContext::constExp(size_t i) {
  return getRuleContext<SysYExtendedParser::ConstExpContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ConstDefContext::RBRACKET() {
  return getTokens(SysYExtendedParser::RBRACKET);
}

tree::TerminalNode* SysYExtendedParser::ConstDefContext::RBRACKET(size_t i) {
  return getToken(SysYExtendedParser::RBRACKET, i);
}


size_t SysYExtendedParser::ConstDefContext::getRuleIndex() const {
  return SysYExtendedParser::RuleConstDef;
}

void SysYExtendedParser::ConstDefContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterConstDef(this);
}

void SysYExtendedParser::ConstDefContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitConstDef(this);
}


antlrcpp::Any SysYExtendedParser::ConstDefContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitConstDef(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ConstDefContext* SysYExtendedParser::constDef() {
  ConstDefContext *_localctx = _tracker.createInstance<ConstDefContext>(_ctx, getState());
  enterRule(_localctx, 10, SysYExtendedParser::RuleConstDef);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(103);
    match(SysYExtendedParser::IDENT);
    setState(110);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while (_la == SysYExtendedParser::LBRACKET) {
      setState(104);
      match(SysYExtendedParser::LBRACKET);
      setState(105);
      constExp();
      setState(106);
      match(SysYExtendedParser::RBRACKET);
      setState(112);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
    setState(113);
    match(SysYExtendedParser::ASSIGN);
    setState(114);
    constInitVal();
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ConstInitValContext ------------------------------------------------------------------

SysYExtendedParser::ConstInitValContext::ConstInitValContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::ConstExpContext* SysYExtendedParser::ConstInitValContext::constExp() {
  return getRuleContext<SysYExtendedParser::ConstExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::ConstInitValContext::LBRACE() {
  return getToken(SysYExtendedParser::LBRACE, 0);
}

tree::TerminalNode* SysYExtendedParser::ConstInitValContext::RBRACE() {
  return getToken(SysYExtendedParser::RBRACE, 0);
}

std::vector<SysYExtendedParser::ConstInitValContext *> SysYExtendedParser::ConstInitValContext::constInitVal() {
  return getRuleContexts<SysYExtendedParser::ConstInitValContext>();
}

SysYExtendedParser::ConstInitValContext* SysYExtendedParser::ConstInitValContext::constInitVal(size_t i) {
  return getRuleContext<SysYExtendedParser::ConstInitValContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ConstInitValContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::ConstInitValContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::ConstInitValContext::getRuleIndex() const {
  return SysYExtendedParser::RuleConstInitVal;
}

void SysYExtendedParser::ConstInitValContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterConstInitVal(this);
}

void SysYExtendedParser::ConstInitValContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitConstInitVal(this);
}


antlrcpp::Any SysYExtendedParser::ConstInitValContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitConstInitVal(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ConstInitValContext* SysYExtendedParser::constInitVal() {
  ConstInitValContext *_localctx = _tracker.createInstance<ConstInitValContext>(_ctx, getState());
  enterRule(_localctx, 12, SysYExtendedParser::RuleConstInitVal);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(129);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::LPAREN:
      case SysYExtendedParser::ADD:
      case SysYExtendedParser::SUB:
      case SysYExtendedParser::NOT:
      case SysYExtendedParser::IDENT:
      case SysYExtendedParser::INTCONST:
      case SysYExtendedParser::FLOATCONST: {
        enterOuterAlt(_localctx, 1);
        setState(116);
        constExp();
        break;
      }

      case SysYExtendedParser::LBRACE: {
        enterOuterAlt(_localctx, 2);
        setState(117);
        match(SysYExtendedParser::LBRACE);
        setState(126);
        _errHandler->sync(this);

        _la = _input->LA(1);
        if ((((_la & ~ 0x3fULL) == 0) &&
          ((1ULL << _la) & ((1ULL << SysYExtendedParser::LPAREN)
          | (1ULL << SysYExtendedParser::LBRACE)
          | (1ULL << SysYExtendedParser::ADD)
          | (1ULL << SysYExtendedParser::SUB)
          | (1ULL << SysYExtendedParser::NOT)
          | (1ULL << SysYExtendedParser::IDENT)
          | (1ULL << SysYExtendedParser::INTCONST)
          | (1ULL << SysYExtendedParser::FLOATCONST))) != 0)) {
          setState(118);
          constInitVal();
          setState(123);
          _errHandler->sync(this);
          _la = _input->LA(1);
          while (_la == SysYExtendedParser::COMMA) {
            setState(119);
            match(SysYExtendedParser::COMMA);
            setState(120);
            constInitVal();
            setState(125);
            _errHandler->sync(this);
            _la = _input->LA(1);
          }
        }
        setState(128);
        match(SysYExtendedParser::RBRACE);
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- VarDeclContext ------------------------------------------------------------------

SysYExtendedParser::VarDeclContext::VarDeclContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::BTypeContext* SysYExtendedParser::VarDeclContext::bType() {
  return getRuleContext<SysYExtendedParser::BTypeContext>(0);
}

std::vector<SysYExtendedParser::VarDefContext *> SysYExtendedParser::VarDeclContext::varDef() {
  return getRuleContexts<SysYExtendedParser::VarDefContext>();
}

SysYExtendedParser::VarDefContext* SysYExtendedParser::VarDeclContext::varDef(size_t i) {
  return getRuleContext<SysYExtendedParser::VarDefContext>(i);
}

tree::TerminalNode* SysYExtendedParser::VarDeclContext::SEMICOLON() {
  return getToken(SysYExtendedParser::SEMICOLON, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::VarDeclContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::VarDeclContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::VarDeclContext::getRuleIndex() const {
  return SysYExtendedParser::RuleVarDecl;
}

void SysYExtendedParser::VarDeclContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterVarDecl(this);
}

void SysYExtendedParser::VarDeclContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitVarDecl(this);
}


antlrcpp::Any SysYExtendedParser::VarDeclContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitVarDecl(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::VarDeclContext* SysYExtendedParser::varDecl() {
  VarDeclContext *_localctx = _tracker.createInstance<VarDeclContext>(_ctx, getState());
  enterRule(_localctx, 14, SysYExtendedParser::RuleVarDecl);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(131);
    bType();
    setState(132);
    varDef();
    setState(137);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while (_la == SysYExtendedParser::COMMA) {
      setState(133);
      match(SysYExtendedParser::COMMA);
      setState(134);
      varDef();
      setState(139);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
    setState(140);
    match(SysYExtendedParser::SEMICOLON);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- VarDefContext ------------------------------------------------------------------

SysYExtendedParser::VarDefContext::VarDefContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::VarDefContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::VarDefContext::LBRACKET() {
  return getTokens(SysYExtendedParser::LBRACKET);
}

tree::TerminalNode* SysYExtendedParser::VarDefContext::LBRACKET(size_t i) {
  return getToken(SysYExtendedParser::LBRACKET, i);
}

std::vector<SysYExtendedParser::ConstExpContext *> SysYExtendedParser::VarDefContext::constExp() {
  return getRuleContexts<SysYExtendedParser::ConstExpContext>();
}

SysYExtendedParser::ConstExpContext* SysYExtendedParser::VarDefContext::constExp(size_t i) {
  return getRuleContext<SysYExtendedParser::ConstExpContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::VarDefContext::RBRACKET() {
  return getTokens(SysYExtendedParser::RBRACKET);
}

tree::TerminalNode* SysYExtendedParser::VarDefContext::RBRACKET(size_t i) {
  return getToken(SysYExtendedParser::RBRACKET, i);
}

tree::TerminalNode* SysYExtendedParser::VarDefContext::ASSIGN() {
  return getToken(SysYExtendedParser::ASSIGN, 0);
}

SysYExtendedParser::InitValContext* SysYExtendedParser::VarDefContext::initVal() {
  return getRuleContext<SysYExtendedParser::InitValContext>(0);
}


size_t SysYExtendedParser::VarDefContext::getRuleIndex() const {
  return SysYExtendedParser::RuleVarDef;
}

void SysYExtendedParser::VarDefContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterVarDef(this);
}

void SysYExtendedParser::VarDefContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitVarDef(this);
}


antlrcpp::Any SysYExtendedParser::VarDefContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitVarDef(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::VarDefContext* SysYExtendedParser::varDef() {
  VarDefContext *_localctx = _tracker.createInstance<VarDefContext>(_ctx, getState());
  enterRule(_localctx, 16, SysYExtendedParser::RuleVarDef);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(164);
    _errHandler->sync(this);
    switch (getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 11, _ctx)) {
    case 1: {
      enterOuterAlt(_localctx, 1);
      setState(142);
      match(SysYExtendedParser::IDENT);
      setState(149);
      _errHandler->sync(this);
      _la = _input->LA(1);
      while (_la == SysYExtendedParser::LBRACKET) {
        setState(143);
        match(SysYExtendedParser::LBRACKET);
        setState(144);
        constExp();
        setState(145);
        match(SysYExtendedParser::RBRACKET);
        setState(151);
        _errHandler->sync(this);
        _la = _input->LA(1);
      }
      break;
    }

    case 2: {
      enterOuterAlt(_localctx, 2);
      setState(152);
      match(SysYExtendedParser::IDENT);
      setState(159);
      _errHandler->sync(this);
      _la = _input->LA(1);
      while (_la == SysYExtendedParser::LBRACKET) {
        setState(153);
        match(SysYExtendedParser::LBRACKET);
        setState(154);
        constExp();
        setState(155);
        match(SysYExtendedParser::RBRACKET);
        setState(161);
        _errHandler->sync(this);
        _la = _input->LA(1);
      }
      setState(162);
      match(SysYExtendedParser::ASSIGN);
      setState(163);
      initVal();
      break;
    }

    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- InitValContext ------------------------------------------------------------------

SysYExtendedParser::InitValContext::InitValContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::ExpContext* SysYExtendedParser::InitValContext::exp() {
  return getRuleContext<SysYExtendedParser::ExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::InitValContext::LBRACE() {
  return getToken(SysYExtendedParser::LBRACE, 0);
}

tree::TerminalNode* SysYExtendedParser::InitValContext::RBRACE() {
  return getToken(SysYExtendedParser::RBRACE, 0);
}

std::vector<SysYExtendedParser::InitValContext *> SysYExtendedParser::InitValContext::initVal() {
  return getRuleContexts<SysYExtendedParser::InitValContext>();
}

SysYExtendedParser::InitValContext* SysYExtendedParser::InitValContext::initVal(size_t i) {
  return getRuleContext<SysYExtendedParser::InitValContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::InitValContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::InitValContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::InitValContext::getRuleIndex() const {
  return SysYExtendedParser::RuleInitVal;
}

void SysYExtendedParser::InitValContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterInitVal(this);
}

void SysYExtendedParser::InitValContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitInitVal(this);
}


antlrcpp::Any SysYExtendedParser::InitValContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitInitVal(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::InitValContext* SysYExtendedParser::initVal() {
  InitValContext *_localctx = _tracker.createInstance<InitValContext>(_ctx, getState());
  enterRule(_localctx, 18, SysYExtendedParser::RuleInitVal);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(179);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::LPAREN:
      case SysYExtendedParser::ADD:
      case SysYExtendedParser::SUB:
      case SysYExtendedParser::NOT:
      case SysYExtendedParser::IDENT:
      case SysYExtendedParser::INTCONST:
      case SysYExtendedParser::FLOATCONST: {
        enterOuterAlt(_localctx, 1);
        setState(166);
        exp();
        break;
      }

      case SysYExtendedParser::LBRACE: {
        enterOuterAlt(_localctx, 2);
        setState(167);
        match(SysYExtendedParser::LBRACE);
        setState(176);
        _errHandler->sync(this);

        _la = _input->LA(1);
        if ((((_la & ~ 0x3fULL) == 0) &&
          ((1ULL << _la) & ((1ULL << SysYExtendedParser::LPAREN)
          | (1ULL << SysYExtendedParser::LBRACE)
          | (1ULL << SysYExtendedParser::ADD)
          | (1ULL << SysYExtendedParser::SUB)
          | (1ULL << SysYExtendedParser::NOT)
          | (1ULL << SysYExtendedParser::IDENT)
          | (1ULL << SysYExtendedParser::INTCONST)
          | (1ULL << SysYExtendedParser::FLOATCONST))) != 0)) {
          setState(168);
          initVal();
          setState(173);
          _errHandler->sync(this);
          _la = _input->LA(1);
          while (_la == SysYExtendedParser::COMMA) {
            setState(169);
            match(SysYExtendedParser::COMMA);
            setState(170);
            initVal();
            setState(175);
            _errHandler->sync(this);
            _la = _input->LA(1);
          }
        }
        setState(178);
        match(SysYExtendedParser::RBRACE);
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- FuncDefContext ------------------------------------------------------------------

SysYExtendedParser::FuncDefContext::FuncDefContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::FuncTypeContext* SysYExtendedParser::FuncDefContext::funcType() {
  return getRuleContext<SysYExtendedParser::FuncTypeContext>(0);
}

tree::TerminalNode* SysYExtendedParser::FuncDefContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

tree::TerminalNode* SysYExtendedParser::FuncDefContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

tree::TerminalNode* SysYExtendedParser::FuncDefContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

SysYExtendedParser::BlockContext* SysYExtendedParser::FuncDefContext::block() {
  return getRuleContext<SysYExtendedParser::BlockContext>(0);
}

SysYExtendedParser::FuncFormalParamsContext* SysYExtendedParser::FuncDefContext::funcFormalParams() {
  return getRuleContext<SysYExtendedParser::FuncFormalParamsContext>(0);
}


size_t SysYExtendedParser::FuncDefContext::getRuleIndex() const {
  return SysYExtendedParser::RuleFuncDef;
}

void SysYExtendedParser::FuncDefContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterFuncDef(this);
}

void SysYExtendedParser::FuncDefContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitFuncDef(this);
}


antlrcpp::Any SysYExtendedParser::FuncDefContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitFuncDef(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::FuncDefContext* SysYExtendedParser::funcDef() {
  FuncDefContext *_localctx = _tracker.createInstance<FuncDefContext>(_ctx, getState());
  enterRule(_localctx, 20, SysYExtendedParser::RuleFuncDef);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(181);
    funcType();
    setState(182);
    match(SysYExtendedParser::IDENT);
    setState(183);
    match(SysYExtendedParser::LPAREN);
    setState(185);
    _errHandler->sync(this);

    _la = _input->LA(1);
    if (_la == SysYExtendedParser::INT

    || _la == SysYExtendedParser::FLOAT) {
      setState(184);
      funcFormalParams();
    }
    setState(187);
    match(SysYExtendedParser::RPAREN);
    setState(188);
    block();
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- FuncTypeContext ------------------------------------------------------------------

SysYExtendedParser::FuncTypeContext::FuncTypeContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::FuncTypeContext::VOID() {
  return getToken(SysYExtendedParser::VOID, 0);
}

SysYExtendedParser::BTypeContext* SysYExtendedParser::FuncTypeContext::bType() {
  return getRuleContext<SysYExtendedParser::BTypeContext>(0);
}


size_t SysYExtendedParser::FuncTypeContext::getRuleIndex() const {
  return SysYExtendedParser::RuleFuncType;
}

void SysYExtendedParser::FuncTypeContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterFuncType(this);
}

void SysYExtendedParser::FuncTypeContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitFuncType(this);
}


antlrcpp::Any SysYExtendedParser::FuncTypeContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitFuncType(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::FuncTypeContext* SysYExtendedParser::funcType() {
  FuncTypeContext *_localctx = _tracker.createInstance<FuncTypeContext>(_ctx, getState());
  enterRule(_localctx, 22, SysYExtendedParser::RuleFuncType);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(192);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::VOID: {
        enterOuterAlt(_localctx, 1);
        setState(190);
        match(SysYExtendedParser::VOID);
        break;
      }

      case SysYExtendedParser::INT:
      case SysYExtendedParser::FLOAT: {
        enterOuterAlt(_localctx, 2);
        setState(191);
        bType();
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- FuncFormalParamsContext ------------------------------------------------------------------

SysYExtendedParser::FuncFormalParamsContext::FuncFormalParamsContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

std::vector<SysYExtendedParser::FuncFormalParamContext *> SysYExtendedParser::FuncFormalParamsContext::funcFormalParam() {
  return getRuleContexts<SysYExtendedParser::FuncFormalParamContext>();
}

SysYExtendedParser::FuncFormalParamContext* SysYExtendedParser::FuncFormalParamsContext::funcFormalParam(size_t i) {
  return getRuleContext<SysYExtendedParser::FuncFormalParamContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::FuncFormalParamsContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::FuncFormalParamsContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::FuncFormalParamsContext::getRuleIndex() const {
  return SysYExtendedParser::RuleFuncFormalParams;
}

void SysYExtendedParser::FuncFormalParamsContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterFuncFormalParams(this);
}

void SysYExtendedParser::FuncFormalParamsContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitFuncFormalParams(this);
}


antlrcpp::Any SysYExtendedParser::FuncFormalParamsContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitFuncFormalParams(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::FuncFormalParamsContext* SysYExtendedParser::funcFormalParams() {
  FuncFormalParamsContext *_localctx = _tracker.createInstance<FuncFormalParamsContext>(_ctx, getState());
  enterRule(_localctx, 24, SysYExtendedParser::RuleFuncFormalParams);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(194);
    funcFormalParam();
    setState(199);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while (_la == SysYExtendedParser::COMMA) {
      setState(195);
      match(SysYExtendedParser::COMMA);
      setState(196);
      funcFormalParam();
      setState(201);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- FuncFormalParamContext ------------------------------------------------------------------

SysYExtendedParser::FuncFormalParamContext::FuncFormalParamContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::BTypeContext* SysYExtendedParser::FuncFormalParamContext::bType() {
  return getRuleContext<SysYExtendedParser::BTypeContext>(0);
}

tree::TerminalNode* SysYExtendedParser::FuncFormalParamContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::FuncFormalParamContext::LBRACKET() {
  return getTokens(SysYExtendedParser::LBRACKET);
}

tree::TerminalNode* SysYExtendedParser::FuncFormalParamContext::LBRACKET(size_t i) {
  return getToken(SysYExtendedParser::LBRACKET, i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::FuncFormalParamContext::RBRACKET() {
  return getTokens(SysYExtendedParser::RBRACKET);
}

tree::TerminalNode* SysYExtendedParser::FuncFormalParamContext::RBRACKET(size_t i) {
  return getToken(SysYExtendedParser::RBRACKET, i);
}

std::vector<SysYExtendedParser::ExpContext *> SysYExtendedParser::FuncFormalParamContext::exp() {
  return getRuleContexts<SysYExtendedParser::ExpContext>();
}

SysYExtendedParser::ExpContext* SysYExtendedParser::FuncFormalParamContext::exp(size_t i) {
  return getRuleContext<SysYExtendedParser::ExpContext>(i);
}


size_t SysYExtendedParser::FuncFormalParamContext::getRuleIndex() const {
  return SysYExtendedParser::RuleFuncFormalParam;
}

void SysYExtendedParser::FuncFormalParamContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterFuncFormalParam(this);
}

void SysYExtendedParser::FuncFormalParamContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitFuncFormalParam(this);
}


antlrcpp::Any SysYExtendedParser::FuncFormalParamContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitFuncFormalParam(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::FuncFormalParamContext* SysYExtendedParser::funcFormalParam() {
  FuncFormalParamContext *_localctx = _tracker.createInstance<FuncFormalParamContext>(_ctx, getState());
  enterRule(_localctx, 26, SysYExtendedParser::RuleFuncFormalParam);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(202);
    bType();
    setState(203);
    match(SysYExtendedParser::IDENT);
    setState(215);
    _errHandler->sync(this);

    _la = _input->LA(1);
    if (_la == SysYExtendedParser::LBRACKET) {
      setState(204);
      match(SysYExtendedParser::LBRACKET);
      setState(205);
      match(SysYExtendedParser::RBRACKET);
      setState(212);
      _errHandler->sync(this);
      _la = _input->LA(1);
      while (_la == SysYExtendedParser::LBRACKET) {
        setState(206);
        match(SysYExtendedParser::LBRACKET);
        setState(207);
        exp();
        setState(208);
        match(SysYExtendedParser::RBRACKET);
        setState(214);
        _errHandler->sync(this);
        _la = _input->LA(1);
      }
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- BlockContext ------------------------------------------------------------------

SysYExtendedParser::BlockContext::BlockContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::BlockContext::LBRACE() {
  return getToken(SysYExtendedParser::LBRACE, 0);
}

tree::TerminalNode* SysYExtendedParser::BlockContext::RBRACE() {
  return getToken(SysYExtendedParser::RBRACE, 0);
}

std::vector<SysYExtendedParser::BlockItemContext *> SysYExtendedParser::BlockContext::blockItem() {
  return getRuleContexts<SysYExtendedParser::BlockItemContext>();
}

SysYExtendedParser::BlockItemContext* SysYExtendedParser::BlockContext::blockItem(size_t i) {
  return getRuleContext<SysYExtendedParser::BlockItemContext>(i);
}


size_t SysYExtendedParser::BlockContext::getRuleIndex() const {
  return SysYExtendedParser::RuleBlock;
}

void SysYExtendedParser::BlockContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterBlock(this);
}

void SysYExtendedParser::BlockContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitBlock(this);
}


antlrcpp::Any SysYExtendedParser::BlockContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitBlock(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::BlockContext* SysYExtendedParser::block() {
  BlockContext *_localctx = _tracker.createInstance<BlockContext>(_ctx, getState());
  enterRule(_localctx, 28, SysYExtendedParser::RuleBlock);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(217);
    match(SysYExtendedParser::LBRACE);
    setState(221);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while ((((_la & ~ 0x3fULL) == 0) &&
      ((1ULL << _la) & ((1ULL << SysYExtendedParser::INT)
      | (1ULL << SysYExtendedParser::FLOAT)
      | (1ULL << SysYExtendedParser::CONST)
      | (1ULL << SysYExtendedParser::IF)
      | (1ULL << SysYExtendedParser::WHILE)
      | (1ULL << SysYExtendedParser::FOR)
      | (1ULL << SysYExtendedParser::BREAK)
      | (1ULL << SysYExtendedParser::CONTINUE)
      | (1ULL << SysYExtendedParser::RETURN)
      | (1ULL << SysYExtendedParser::PRAGMA)
      | (1ULL << SysYExtendedParser::BARRIER)
      | (1ULL << SysYExtendedParser::LPAREN)
      | (1ULL << SysYExtendedParser::LBRACE)
      | (1ULL << SysYExtendedParser::ADD)
      | (1ULL << SysYExtendedParser::SUB)
      | (1ULL << SysYExtendedParser::NOT)
      | (1ULL << SysYExtendedParser::IDENT)
      | (1ULL << SysYExtendedParser::INTCONST)
      | (1ULL << SysYExtendedParser::FLOATCONST))) != 0)) {
      setState(218);
      blockItem();
      setState(223);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
    setState(224);
    match(SysYExtendedParser::RBRACE);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- BlockItemContext ------------------------------------------------------------------

SysYExtendedParser::BlockItemContext::BlockItemContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::DeclContext* SysYExtendedParser::BlockItemContext::decl() {
  return getRuleContext<SysYExtendedParser::DeclContext>(0);
}

SysYExtendedParser::StmtContext* SysYExtendedParser::BlockItemContext::stmt() {
  return getRuleContext<SysYExtendedParser::StmtContext>(0);
}


size_t SysYExtendedParser::BlockItemContext::getRuleIndex() const {
  return SysYExtendedParser::RuleBlockItem;
}

void SysYExtendedParser::BlockItemContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterBlockItem(this);
}

void SysYExtendedParser::BlockItemContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitBlockItem(this);
}


antlrcpp::Any SysYExtendedParser::BlockItemContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitBlockItem(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::BlockItemContext* SysYExtendedParser::blockItem() {
  BlockItemContext *_localctx = _tracker.createInstance<BlockItemContext>(_ctx, getState());
  enterRule(_localctx, 30, SysYExtendedParser::RuleBlockItem);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(228);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::INT:
      case SysYExtendedParser::FLOAT:
      case SysYExtendedParser::CONST: {
        enterOuterAlt(_localctx, 1);
        setState(226);
        decl();
        break;
      }

      case SysYExtendedParser::IF:
      case SysYExtendedParser::WHILE:
      case SysYExtendedParser::FOR:
      case SysYExtendedParser::BREAK:
      case SysYExtendedParser::CONTINUE:
      case SysYExtendedParser::RETURN:
      case SysYExtendedParser::PRAGMA:
      case SysYExtendedParser::BARRIER:
      case SysYExtendedParser::LPAREN:
      case SysYExtendedParser::LBRACE:
      case SysYExtendedParser::ADD:
      case SysYExtendedParser::SUB:
      case SysYExtendedParser::NOT:
      case SysYExtendedParser::IDENT:
      case SysYExtendedParser::INTCONST:
      case SysYExtendedParser::FLOATCONST: {
        enterOuterAlt(_localctx, 2);
        setState(227);
        stmt();
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- StmtContext ------------------------------------------------------------------

SysYExtendedParser::StmtContext::StmtContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::LValContext* SysYExtendedParser::StmtContext::lVal() {
  return getRuleContext<SysYExtendedParser::LValContext>(0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::ASSIGN() {
  return getToken(SysYExtendedParser::ASSIGN, 0);
}

SysYExtendedParser::ExpContext* SysYExtendedParser::StmtContext::exp() {
  return getRuleContext<SysYExtendedParser::ExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::SEMICOLON() {
  return getToken(SysYExtendedParser::SEMICOLON, 0);
}

SysYExtendedParser::BlockContext* SysYExtendedParser::StmtContext::block() {
  return getRuleContext<SysYExtendedParser::BlockContext>(0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::IF() {
  return getToken(SysYExtendedParser::IF, 0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

SysYExtendedParser::CondContext* SysYExtendedParser::StmtContext::cond() {
  return getRuleContext<SysYExtendedParser::CondContext>(0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

std::vector<SysYExtendedParser::StmtContext *> SysYExtendedParser::StmtContext::stmt() {
  return getRuleContexts<SysYExtendedParser::StmtContext>();
}

SysYExtendedParser::StmtContext* SysYExtendedParser::StmtContext::stmt(size_t i) {
  return getRuleContext<SysYExtendedParser::StmtContext>(i);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::ELSE() {
  return getToken(SysYExtendedParser::ELSE, 0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::WHILE() {
  return getToken(SysYExtendedParser::WHILE, 0);
}

SysYExtendedParser::ForStmtContext* SysYExtendedParser::StmtContext::forStmt() {
  return getRuleContext<SysYExtendedParser::ForStmtContext>(0);
}

SysYExtendedParser::ParallelForStmtContext* SysYExtendedParser::StmtContext::parallelForStmt() {
  return getRuleContext<SysYExtendedParser::ParallelForStmtContext>(0);
}

SysYExtendedParser::BarrierStmtContext* SysYExtendedParser::StmtContext::barrierStmt() {
  return getRuleContext<SysYExtendedParser::BarrierStmtContext>(0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::BREAK() {
  return getToken(SysYExtendedParser::BREAK, 0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::CONTINUE() {
  return getToken(SysYExtendedParser::CONTINUE, 0);
}

tree::TerminalNode* SysYExtendedParser::StmtContext::RETURN() {
  return getToken(SysYExtendedParser::RETURN, 0);
}


size_t SysYExtendedParser::StmtContext::getRuleIndex() const {
  return SysYExtendedParser::RuleStmt;
}

void SysYExtendedParser::StmtContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterStmt(this);
}

void SysYExtendedParser::StmtContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitStmt(this);
}


antlrcpp::Any SysYExtendedParser::StmtContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitStmt(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::StmtContext* SysYExtendedParser::stmt() {
  StmtContext *_localctx = _tracker.createInstance<StmtContext>(_ctx, getState());
  enterRule(_localctx, 32, SysYExtendedParser::RuleStmt);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(266);
    _errHandler->sync(this);
    switch (getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 24, _ctx)) {
    case 1: {
      enterOuterAlt(_localctx, 1);
      setState(230);
      lVal();
      setState(231);
      match(SysYExtendedParser::ASSIGN);
      setState(232);
      exp();
      setState(233);
      match(SysYExtendedParser::SEMICOLON);
      break;
    }

    case 2: {
      enterOuterAlt(_localctx, 2);
      setState(235);
      exp();
      setState(236);
      match(SysYExtendedParser::SEMICOLON);
      break;
    }

    case 3: {
      enterOuterAlt(_localctx, 3);
      setState(238);
      block();
      break;
    }

    case 4: {
      enterOuterAlt(_localctx, 4);
      setState(239);
      match(SysYExtendedParser::IF);
      setState(240);
      match(SysYExtendedParser::LPAREN);
      setState(241);
      cond();
      setState(242);
      match(SysYExtendedParser::RPAREN);
      setState(243);
      stmt();
      setState(246);
      _errHandler->sync(this);

      switch (getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 22, _ctx)) {
      case 1: {
        setState(244);
        match(SysYExtendedParser::ELSE);
        setState(245);
        stmt();
        break;
      }

      }
      break;
    }

    case 5: {
      enterOuterAlt(_localctx, 5);
      setState(248);
      match(SysYExtendedParser::WHILE);
      setState(249);
      match(SysYExtendedParser::LPAREN);
      setState(250);
      cond();
      setState(251);
      match(SysYExtendedParser::RPAREN);
      setState(252);
      stmt();
      break;
    }

    case 6: {
      enterOuterAlt(_localctx, 6);
      setState(254);
      forStmt();
      break;
    }

    case 7: {
      enterOuterAlt(_localctx, 7);
      setState(255);
      parallelForStmt();
      break;
    }

    case 8: {
      enterOuterAlt(_localctx, 8);
      setState(256);
      barrierStmt();
      break;
    }

    case 9: {
      enterOuterAlt(_localctx, 9);
      setState(257);
      match(SysYExtendedParser::BREAK);
      setState(258);
      match(SysYExtendedParser::SEMICOLON);
      break;
    }

    case 10: {
      enterOuterAlt(_localctx, 10);
      setState(259);
      match(SysYExtendedParser::CONTINUE);
      setState(260);
      match(SysYExtendedParser::SEMICOLON);
      break;
    }

    case 11: {
      enterOuterAlt(_localctx, 11);
      setState(261);
      match(SysYExtendedParser::RETURN);
      setState(263);
      _errHandler->sync(this);

      _la = _input->LA(1);
      if ((((_la & ~ 0x3fULL) == 0) &&
        ((1ULL << _la) & ((1ULL << SysYExtendedParser::LPAREN)
        | (1ULL << SysYExtendedParser::ADD)
        | (1ULL << SysYExtendedParser::SUB)
        | (1ULL << SysYExtendedParser::NOT)
        | (1ULL << SysYExtendedParser::IDENT)
        | (1ULL << SysYExtendedParser::INTCONST)
        | (1ULL << SysYExtendedParser::FLOATCONST))) != 0)) {
        setState(262);
        exp();
      }
      setState(265);
      match(SysYExtendedParser::SEMICOLON);
      break;
    }

    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ExpContext ------------------------------------------------------------------

SysYExtendedParser::ExpContext::ExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::AddExpContext* SysYExtendedParser::ExpContext::addExp() {
  return getRuleContext<SysYExtendedParser::AddExpContext>(0);
}


size_t SysYExtendedParser::ExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleExp;
}

void SysYExtendedParser::ExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterExp(this);
}

void SysYExtendedParser::ExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitExp(this);
}


antlrcpp::Any SysYExtendedParser::ExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitExp(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ExpContext* SysYExtendedParser::exp() {
  ExpContext *_localctx = _tracker.createInstance<ExpContext>(_ctx, getState());
  enterRule(_localctx, 34, SysYExtendedParser::RuleExp);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(268);
    addExp(0);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- CondContext ------------------------------------------------------------------

SysYExtendedParser::CondContext::CondContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::LOrExpContext* SysYExtendedParser::CondContext::lOrExp() {
  return getRuleContext<SysYExtendedParser::LOrExpContext>(0);
}


size_t SysYExtendedParser::CondContext::getRuleIndex() const {
  return SysYExtendedParser::RuleCond;
}

void SysYExtendedParser::CondContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterCond(this);
}

void SysYExtendedParser::CondContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitCond(this);
}


antlrcpp::Any SysYExtendedParser::CondContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitCond(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::CondContext* SysYExtendedParser::cond() {
  CondContext *_localctx = _tracker.createInstance<CondContext>(_ctx, getState());
  enterRule(_localctx, 36, SysYExtendedParser::RuleCond);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(270);
    lOrExp(0);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- LValContext ------------------------------------------------------------------

SysYExtendedParser::LValContext::LValContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::LValContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::LValContext::LBRACKET() {
  return getTokens(SysYExtendedParser::LBRACKET);
}

tree::TerminalNode* SysYExtendedParser::LValContext::LBRACKET(size_t i) {
  return getToken(SysYExtendedParser::LBRACKET, i);
}

std::vector<SysYExtendedParser::ExpContext *> SysYExtendedParser::LValContext::exp() {
  return getRuleContexts<SysYExtendedParser::ExpContext>();
}

SysYExtendedParser::ExpContext* SysYExtendedParser::LValContext::exp(size_t i) {
  return getRuleContext<SysYExtendedParser::ExpContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::LValContext::RBRACKET() {
  return getTokens(SysYExtendedParser::RBRACKET);
}

tree::TerminalNode* SysYExtendedParser::LValContext::RBRACKET(size_t i) {
  return getToken(SysYExtendedParser::RBRACKET, i);
}


size_t SysYExtendedParser::LValContext::getRuleIndex() const {
  return SysYExtendedParser::RuleLVal;
}

void SysYExtendedParser::LValContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterLVal(this);
}

void SysYExtendedParser::LValContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitLVal(this);
}


antlrcpp::Any SysYExtendedParser::LValContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitLVal(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::LValContext* SysYExtendedParser::lVal() {
  LValContext *_localctx = _tracker.createInstance<LValContext>(_ctx, getState());
  enterRule(_localctx, 38, SysYExtendedParser::RuleLVal);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(272);
    match(SysYExtendedParser::IDENT);
    setState(279);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 25, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        setState(273);
        match(SysYExtendedParser::LBRACKET);
        setState(274);
        exp();
        setState(275);
        match(SysYExtendedParser::RBRACKET); 
      }
      setState(281);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 25, _ctx);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- PrimaryExpContext ------------------------------------------------------------------

SysYExtendedParser::PrimaryExpContext::PrimaryExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::PrimaryExpContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

SysYExtendedParser::ExpContext* SysYExtendedParser::PrimaryExpContext::exp() {
  return getRuleContext<SysYExtendedParser::ExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::PrimaryExpContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

SysYExtendedParser::LValContext* SysYExtendedParser::PrimaryExpContext::lVal() {
  return getRuleContext<SysYExtendedParser::LValContext>(0);
}

SysYExtendedParser::NumberContext* SysYExtendedParser::PrimaryExpContext::number() {
  return getRuleContext<SysYExtendedParser::NumberContext>(0);
}


size_t SysYExtendedParser::PrimaryExpContext::getRuleIndex() const {
  return SysYExtendedParser::RulePrimaryExp;
}

void SysYExtendedParser::PrimaryExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterPrimaryExp(this);
}

void SysYExtendedParser::PrimaryExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitPrimaryExp(this);
}


antlrcpp::Any SysYExtendedParser::PrimaryExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitPrimaryExp(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::PrimaryExpContext* SysYExtendedParser::primaryExp() {
  PrimaryExpContext *_localctx = _tracker.createInstance<PrimaryExpContext>(_ctx, getState());
  enterRule(_localctx, 40, SysYExtendedParser::RulePrimaryExp);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(288);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::LPAREN: {
        enterOuterAlt(_localctx, 1);
        setState(282);
        match(SysYExtendedParser::LPAREN);
        setState(283);
        exp();
        setState(284);
        match(SysYExtendedParser::RPAREN);
        break;
      }

      case SysYExtendedParser::IDENT: {
        enterOuterAlt(_localctx, 2);
        setState(286);
        lVal();
        break;
      }

      case SysYExtendedParser::INTCONST:
      case SysYExtendedParser::FLOATCONST: {
        enterOuterAlt(_localctx, 3);
        setState(287);
        number();
        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- NumberContext ------------------------------------------------------------------

SysYExtendedParser::NumberContext::NumberContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::NumberContext::INTCONST() {
  return getToken(SysYExtendedParser::INTCONST, 0);
}

tree::TerminalNode* SysYExtendedParser::NumberContext::FLOATCONST() {
  return getToken(SysYExtendedParser::FLOATCONST, 0);
}


size_t SysYExtendedParser::NumberContext::getRuleIndex() const {
  return SysYExtendedParser::RuleNumber;
}

void SysYExtendedParser::NumberContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterNumber(this);
}

void SysYExtendedParser::NumberContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitNumber(this);
}


antlrcpp::Any SysYExtendedParser::NumberContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitNumber(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::NumberContext* SysYExtendedParser::number() {
  NumberContext *_localctx = _tracker.createInstance<NumberContext>(_ctx, getState());
  enterRule(_localctx, 42, SysYExtendedParser::RuleNumber);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(290);
    _la = _input->LA(1);
    if (!(_la == SysYExtendedParser::INTCONST

    || _la == SysYExtendedParser::FLOATCONST)) {
    _errHandler->recoverInline(this);
    }
    else {
      _errHandler->reportMatch(this);
      consume();
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- UnaryExpContext ------------------------------------------------------------------

SysYExtendedParser::UnaryExpContext::UnaryExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::PrimaryExpContext* SysYExtendedParser::UnaryExpContext::primaryExp() {
  return getRuleContext<SysYExtendedParser::PrimaryExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::UnaryExpContext::IDENT() {
  return getToken(SysYExtendedParser::IDENT, 0);
}

tree::TerminalNode* SysYExtendedParser::UnaryExpContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

tree::TerminalNode* SysYExtendedParser::UnaryExpContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

SysYExtendedParser::FuncRealParamsContext* SysYExtendedParser::UnaryExpContext::funcRealParams() {
  return getRuleContext<SysYExtendedParser::FuncRealParamsContext>(0);
}

SysYExtendedParser::UnaryOPContext* SysYExtendedParser::UnaryExpContext::unaryOP() {
  return getRuleContext<SysYExtendedParser::UnaryOPContext>(0);
}

SysYExtendedParser::UnaryExpContext* SysYExtendedParser::UnaryExpContext::unaryExp() {
  return getRuleContext<SysYExtendedParser::UnaryExpContext>(0);
}


size_t SysYExtendedParser::UnaryExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleUnaryExp;
}

void SysYExtendedParser::UnaryExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterUnaryExp(this);
}

void SysYExtendedParser::UnaryExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitUnaryExp(this);
}


antlrcpp::Any SysYExtendedParser::UnaryExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitUnaryExp(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::UnaryExpContext* SysYExtendedParser::unaryExp() {
  UnaryExpContext *_localctx = _tracker.createInstance<UnaryExpContext>(_ctx, getState());
  enterRule(_localctx, 44, SysYExtendedParser::RuleUnaryExp);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(302);
    _errHandler->sync(this);
    switch (getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 28, _ctx)) {
    case 1: {
      enterOuterAlt(_localctx, 1);
      setState(292);
      primaryExp();
      break;
    }

    case 2: {
      enterOuterAlt(_localctx, 2);
      setState(293);
      match(SysYExtendedParser::IDENT);
      setState(294);
      match(SysYExtendedParser::LPAREN);
      setState(296);
      _errHandler->sync(this);

      _la = _input->LA(1);
      if ((((_la & ~ 0x3fULL) == 0) &&
        ((1ULL << _la) & ((1ULL << SysYExtendedParser::LPAREN)
        | (1ULL << SysYExtendedParser::ADD)
        | (1ULL << SysYExtendedParser::SUB)
        | (1ULL << SysYExtendedParser::NOT)
        | (1ULL << SysYExtendedParser::IDENT)
        | (1ULL << SysYExtendedParser::INTCONST)
        | (1ULL << SysYExtendedParser::FLOATCONST))) != 0)) {
        setState(295);
        funcRealParams();
      }
      setState(298);
      match(SysYExtendedParser::RPAREN);
      break;
    }

    case 3: {
      enterOuterAlt(_localctx, 3);
      setState(299);
      unaryOP();
      setState(300);
      unaryExp();
      break;
    }

    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- UnaryOPContext ------------------------------------------------------------------

SysYExtendedParser::UnaryOPContext::UnaryOPContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::UnaryOPContext::ADD() {
  return getToken(SysYExtendedParser::ADD, 0);
}

tree::TerminalNode* SysYExtendedParser::UnaryOPContext::SUB() {
  return getToken(SysYExtendedParser::SUB, 0);
}

tree::TerminalNode* SysYExtendedParser::UnaryOPContext::NOT() {
  return getToken(SysYExtendedParser::NOT, 0);
}


size_t SysYExtendedParser::UnaryOPContext::getRuleIndex() const {
  return SysYExtendedParser::RuleUnaryOP;
}

void SysYExtendedParser::UnaryOPContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterUnaryOP(this);
}

void SysYExtendedParser::UnaryOPContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitUnaryOP(this);
}


antlrcpp::Any SysYExtendedParser::UnaryOPContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitUnaryOP(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::UnaryOPContext* SysYExtendedParser::unaryOP() {
  UnaryOPContext *_localctx = _tracker.createInstance<UnaryOPContext>(_ctx, getState());
  enterRule(_localctx, 46, SysYExtendedParser::RuleUnaryOP);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(304);
    _la = _input->LA(1);
    if (!((((_la & ~ 0x3fULL) == 0) &&
      ((1ULL << _la) & ((1ULL << SysYExtendedParser::ADD)
      | (1ULL << SysYExtendedParser::SUB)
      | (1ULL << SysYExtendedParser::NOT))) != 0))) {
    _errHandler->recoverInline(this);
    }
    else {
      _errHandler->reportMatch(this);
      consume();
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- FuncRealParamsContext ------------------------------------------------------------------

SysYExtendedParser::FuncRealParamsContext::FuncRealParamsContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

std::vector<SysYExtendedParser::ExpContext *> SysYExtendedParser::FuncRealParamsContext::exp() {
  return getRuleContexts<SysYExtendedParser::ExpContext>();
}

SysYExtendedParser::ExpContext* SysYExtendedParser::FuncRealParamsContext::exp(size_t i) {
  return getRuleContext<SysYExtendedParser::ExpContext>(i);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::FuncRealParamsContext::COMMA() {
  return getTokens(SysYExtendedParser::COMMA);
}

tree::TerminalNode* SysYExtendedParser::FuncRealParamsContext::COMMA(size_t i) {
  return getToken(SysYExtendedParser::COMMA, i);
}


size_t SysYExtendedParser::FuncRealParamsContext::getRuleIndex() const {
  return SysYExtendedParser::RuleFuncRealParams;
}

void SysYExtendedParser::FuncRealParamsContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterFuncRealParams(this);
}

void SysYExtendedParser::FuncRealParamsContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitFuncRealParams(this);
}


antlrcpp::Any SysYExtendedParser::FuncRealParamsContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitFuncRealParams(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::FuncRealParamsContext* SysYExtendedParser::funcRealParams() {
  FuncRealParamsContext *_localctx = _tracker.createInstance<FuncRealParamsContext>(_ctx, getState());
  enterRule(_localctx, 48, SysYExtendedParser::RuleFuncRealParams);
  size_t _la = 0;

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(306);
    exp();
    setState(311);
    _errHandler->sync(this);
    _la = _input->LA(1);
    while (_la == SysYExtendedParser::COMMA) {
      setState(307);
      match(SysYExtendedParser::COMMA);
      setState(308);
      exp();
      setState(313);
      _errHandler->sync(this);
      _la = _input->LA(1);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- MulExpContext ------------------------------------------------------------------

SysYExtendedParser::MulExpContext::MulExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::UnaryExpContext* SysYExtendedParser::MulExpContext::unaryExp() {
  return getRuleContext<SysYExtendedParser::UnaryExpContext>(0);
}

SysYExtendedParser::MulExpContext* SysYExtendedParser::MulExpContext::mulExp() {
  return getRuleContext<SysYExtendedParser::MulExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::MulExpContext::MUL() {
  return getToken(SysYExtendedParser::MUL, 0);
}

tree::TerminalNode* SysYExtendedParser::MulExpContext::DIV() {
  return getToken(SysYExtendedParser::DIV, 0);
}

tree::TerminalNode* SysYExtendedParser::MulExpContext::MOD() {
  return getToken(SysYExtendedParser::MOD, 0);
}


size_t SysYExtendedParser::MulExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleMulExp;
}

void SysYExtendedParser::MulExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterMulExp(this);
}

void SysYExtendedParser::MulExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitMulExp(this);
}


antlrcpp::Any SysYExtendedParser::MulExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitMulExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::MulExpContext* SysYExtendedParser::mulExp() {
   return mulExp(0);
}

SysYExtendedParser::MulExpContext* SysYExtendedParser::mulExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::MulExpContext *_localctx = _tracker.createInstance<MulExpContext>(_ctx, parentState);
  SysYExtendedParser::MulExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 50;
  enterRecursionRule(_localctx, 50, SysYExtendedParser::RuleMulExp, precedence);

    size_t _la = 0;

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(315);
    unaryExp();
    _ctx->stop = _input->LT(-1);
    setState(322);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 30, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<MulExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleMulExp);
        setState(317);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(318);
        _la = _input->LA(1);
        if (!((((_la & ~ 0x3fULL) == 0) &&
          ((1ULL << _la) & ((1ULL << SysYExtendedParser::MUL)
          | (1ULL << SysYExtendedParser::DIV)
          | (1ULL << SysYExtendedParser::MOD))) != 0))) {
        _errHandler->recoverInline(this);
        }
        else {
          _errHandler->reportMatch(this);
          consume();
        }
        setState(319);
        unaryExp(); 
      }
      setState(324);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 30, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- AddExpContext ------------------------------------------------------------------

SysYExtendedParser::AddExpContext::AddExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::MulExpContext* SysYExtendedParser::AddExpContext::mulExp() {
  return getRuleContext<SysYExtendedParser::MulExpContext>(0);
}

SysYExtendedParser::AddExpContext* SysYExtendedParser::AddExpContext::addExp() {
  return getRuleContext<SysYExtendedParser::AddExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::AddExpContext::ADD() {
  return getToken(SysYExtendedParser::ADD, 0);
}

tree::TerminalNode* SysYExtendedParser::AddExpContext::SUB() {
  return getToken(SysYExtendedParser::SUB, 0);
}


size_t SysYExtendedParser::AddExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleAddExp;
}

void SysYExtendedParser::AddExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterAddExp(this);
}

void SysYExtendedParser::AddExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitAddExp(this);
}


antlrcpp::Any SysYExtendedParser::AddExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitAddExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::AddExpContext* SysYExtendedParser::addExp() {
   return addExp(0);
}

SysYExtendedParser::AddExpContext* SysYExtendedParser::addExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::AddExpContext *_localctx = _tracker.createInstance<AddExpContext>(_ctx, parentState);
  SysYExtendedParser::AddExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 52;
  enterRecursionRule(_localctx, 52, SysYExtendedParser::RuleAddExp, precedence);

    size_t _la = 0;

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(326);
    mulExp(0);
    _ctx->stop = _input->LT(-1);
    setState(333);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 31, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<AddExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleAddExp);
        setState(328);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(329);
        _la = _input->LA(1);
        if (!(_la == SysYExtendedParser::ADD

        || _la == SysYExtendedParser::SUB)) {
        _errHandler->recoverInline(this);
        }
        else {
          _errHandler->reportMatch(this);
          consume();
        }
        setState(330);
        mulExp(0); 
      }
      setState(335);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 31, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- RelExpContext ------------------------------------------------------------------

SysYExtendedParser::RelExpContext::RelExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::AddExpContext* SysYExtendedParser::RelExpContext::addExp() {
  return getRuleContext<SysYExtendedParser::AddExpContext>(0);
}

SysYExtendedParser::RelExpContext* SysYExtendedParser::RelExpContext::relExp() {
  return getRuleContext<SysYExtendedParser::RelExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::RelExpContext::LT() {
  return getToken(SysYExtendedParser::LT, 0);
}

tree::TerminalNode* SysYExtendedParser::RelExpContext::GT() {
  return getToken(SysYExtendedParser::GT, 0);
}

tree::TerminalNode* SysYExtendedParser::RelExpContext::LE() {
  return getToken(SysYExtendedParser::LE, 0);
}

tree::TerminalNode* SysYExtendedParser::RelExpContext::GE() {
  return getToken(SysYExtendedParser::GE, 0);
}


size_t SysYExtendedParser::RelExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleRelExp;
}

void SysYExtendedParser::RelExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterRelExp(this);
}

void SysYExtendedParser::RelExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitRelExp(this);
}


antlrcpp::Any SysYExtendedParser::RelExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitRelExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::RelExpContext* SysYExtendedParser::relExp() {
   return relExp(0);
}

SysYExtendedParser::RelExpContext* SysYExtendedParser::relExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::RelExpContext *_localctx = _tracker.createInstance<RelExpContext>(_ctx, parentState);
  SysYExtendedParser::RelExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 54;
  enterRecursionRule(_localctx, 54, SysYExtendedParser::RuleRelExp, precedence);

    size_t _la = 0;

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(337);
    addExp(0);
    _ctx->stop = _input->LT(-1);
    setState(344);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 32, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<RelExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleRelExp);
        setState(339);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(340);
        _la = _input->LA(1);
        if (!((((_la & ~ 0x3fULL) == 0) &&
          ((1ULL << _la) & ((1ULL << SysYExtendedParser::LT)
          | (1ULL << SysYExtendedParser::GT)
          | (1ULL << SysYExtendedParser::LE)
          | (1ULL << SysYExtendedParser::GE))) != 0))) {
        _errHandler->recoverInline(this);
        }
        else {
          _errHandler->reportMatch(this);
          consume();
        }
        setState(341);
        addExp(0); 
      }
      setState(346);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 32, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- EqExpContext ------------------------------------------------------------------

SysYExtendedParser::EqExpContext::EqExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::RelExpContext* SysYExtendedParser::EqExpContext::relExp() {
  return getRuleContext<SysYExtendedParser::RelExpContext>(0);
}

SysYExtendedParser::EqExpContext* SysYExtendedParser::EqExpContext::eqExp() {
  return getRuleContext<SysYExtendedParser::EqExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::EqExpContext::EQ() {
  return getToken(SysYExtendedParser::EQ, 0);
}

tree::TerminalNode* SysYExtendedParser::EqExpContext::NE() {
  return getToken(SysYExtendedParser::NE, 0);
}


size_t SysYExtendedParser::EqExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleEqExp;
}

void SysYExtendedParser::EqExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterEqExp(this);
}

void SysYExtendedParser::EqExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitEqExp(this);
}


antlrcpp::Any SysYExtendedParser::EqExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitEqExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::EqExpContext* SysYExtendedParser::eqExp() {
   return eqExp(0);
}

SysYExtendedParser::EqExpContext* SysYExtendedParser::eqExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::EqExpContext *_localctx = _tracker.createInstance<EqExpContext>(_ctx, parentState);
  SysYExtendedParser::EqExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 56;
  enterRecursionRule(_localctx, 56, SysYExtendedParser::RuleEqExp, precedence);

    size_t _la = 0;

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(348);
    relExp(0);
    _ctx->stop = _input->LT(-1);
    setState(355);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 33, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<EqExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleEqExp);
        setState(350);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(351);
        _la = _input->LA(1);
        if (!(_la == SysYExtendedParser::EQ

        || _la == SysYExtendedParser::NE)) {
        _errHandler->recoverInline(this);
        }
        else {
          _errHandler->reportMatch(this);
          consume();
        }
        setState(352);
        relExp(0); 
      }
      setState(357);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 33, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- LAndExpContext ------------------------------------------------------------------

SysYExtendedParser::LAndExpContext::LAndExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::EqExpContext* SysYExtendedParser::LAndExpContext::eqExp() {
  return getRuleContext<SysYExtendedParser::EqExpContext>(0);
}

SysYExtendedParser::LAndExpContext* SysYExtendedParser::LAndExpContext::lAndExp() {
  return getRuleContext<SysYExtendedParser::LAndExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::LAndExpContext::AND() {
  return getToken(SysYExtendedParser::AND, 0);
}


size_t SysYExtendedParser::LAndExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleLAndExp;
}

void SysYExtendedParser::LAndExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterLAndExp(this);
}

void SysYExtendedParser::LAndExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitLAndExp(this);
}


antlrcpp::Any SysYExtendedParser::LAndExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitLAndExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::LAndExpContext* SysYExtendedParser::lAndExp() {
   return lAndExp(0);
}

SysYExtendedParser::LAndExpContext* SysYExtendedParser::lAndExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::LAndExpContext *_localctx = _tracker.createInstance<LAndExpContext>(_ctx, parentState);
  SysYExtendedParser::LAndExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 58;
  enterRecursionRule(_localctx, 58, SysYExtendedParser::RuleLAndExp, precedence);

    

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(359);
    eqExp(0);
    _ctx->stop = _input->LT(-1);
    setState(366);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 34, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<LAndExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleLAndExp);
        setState(361);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(362);
        match(SysYExtendedParser::AND);
        setState(363);
        eqExp(0); 
      }
      setState(368);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 34, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- LOrExpContext ------------------------------------------------------------------

SysYExtendedParser::LOrExpContext::LOrExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::LAndExpContext* SysYExtendedParser::LOrExpContext::lAndExp() {
  return getRuleContext<SysYExtendedParser::LAndExpContext>(0);
}

SysYExtendedParser::LOrExpContext* SysYExtendedParser::LOrExpContext::lOrExp() {
  return getRuleContext<SysYExtendedParser::LOrExpContext>(0);
}

tree::TerminalNode* SysYExtendedParser::LOrExpContext::OR() {
  return getToken(SysYExtendedParser::OR, 0);
}


size_t SysYExtendedParser::LOrExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleLOrExp;
}

void SysYExtendedParser::LOrExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterLOrExp(this);
}

void SysYExtendedParser::LOrExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitLOrExp(this);
}


antlrcpp::Any SysYExtendedParser::LOrExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitLOrExp(this);
  else
    return visitor->visitChildren(this);
}


SysYExtendedParser::LOrExpContext* SysYExtendedParser::lOrExp() {
   return lOrExp(0);
}

SysYExtendedParser::LOrExpContext* SysYExtendedParser::lOrExp(int precedence) {
  ParserRuleContext *parentContext = _ctx;
  size_t parentState = getState();
  SysYExtendedParser::LOrExpContext *_localctx = _tracker.createInstance<LOrExpContext>(_ctx, parentState);
  SysYExtendedParser::LOrExpContext *previousContext = _localctx;
  (void)previousContext; // Silence compiler, in case the context is not used by generated code.
  size_t startState = 60;
  enterRecursionRule(_localctx, 60, SysYExtendedParser::RuleLOrExp, precedence);

    

  auto onExit = finally([=] {
    unrollRecursionContexts(parentContext);
  });
  try {
    size_t alt;
    enterOuterAlt(_localctx, 1);
    setState(370);
    lAndExp(0);
    _ctx->stop = _input->LT(-1);
    setState(377);
    _errHandler->sync(this);
    alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 35, _ctx);
    while (alt != 2 && alt != atn::ATN::INVALID_ALT_NUMBER) {
      if (alt == 1) {
        if (!_parseListeners.empty())
          triggerExitRuleEvent();
        previousContext = _localctx;
        _localctx = _tracker.createInstance<LOrExpContext>(parentContext, parentState);
        pushNewRecursionContext(_localctx, startState, RuleLOrExp);
        setState(372);

        if (!(precpred(_ctx, 1))) throw FailedPredicateException(this, "precpred(_ctx, 1)");
        setState(373);
        match(SysYExtendedParser::OR);
        setState(374);
        lAndExp(0); 
      }
      setState(379);
      _errHandler->sync(this);
      alt = getInterpreter<atn::ParserATNSimulator>()->adaptivePredict(_input, 35, _ctx);
    }
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }
  return _localctx;
}

//----------------- ConstExpContext ------------------------------------------------------------------

SysYExtendedParser::ConstExpContext::ConstExpContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::AddExpContext* SysYExtendedParser::ConstExpContext::addExp() {
  return getRuleContext<SysYExtendedParser::AddExpContext>(0);
}


size_t SysYExtendedParser::ConstExpContext::getRuleIndex() const {
  return SysYExtendedParser::RuleConstExp;
}

void SysYExtendedParser::ConstExpContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterConstExp(this);
}

void SysYExtendedParser::ConstExpContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitConstExp(this);
}


antlrcpp::Any SysYExtendedParser::ConstExpContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitConstExp(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ConstExpContext* SysYExtendedParser::constExp() {
  ConstExpContext *_localctx = _tracker.createInstance<ConstExpContext>(_ctx, getState());
  enterRule(_localctx, 62, SysYExtendedParser::RuleConstExp);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(380);
    addExp(0);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ForStmtContext ------------------------------------------------------------------

SysYExtendedParser::ForStmtContext::ForStmtContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::ForStmtContext::FOR() {
  return getToken(SysYExtendedParser::FOR, 0);
}

tree::TerminalNode* SysYExtendedParser::ForStmtContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

SysYExtendedParser::ForInitContext* SysYExtendedParser::ForStmtContext::forInit() {
  return getRuleContext<SysYExtendedParser::ForInitContext>(0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ForStmtContext::SEMICOLON() {
  return getTokens(SysYExtendedParser::SEMICOLON);
}

tree::TerminalNode* SysYExtendedParser::ForStmtContext::SEMICOLON(size_t i) {
  return getToken(SysYExtendedParser::SEMICOLON, i);
}

SysYExtendedParser::CondContext* SysYExtendedParser::ForStmtContext::cond() {
  return getRuleContext<SysYExtendedParser::CondContext>(0);
}

SysYExtendedParser::ForUpdateContext* SysYExtendedParser::ForStmtContext::forUpdate() {
  return getRuleContext<SysYExtendedParser::ForUpdateContext>(0);
}

tree::TerminalNode* SysYExtendedParser::ForStmtContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

SysYExtendedParser::StmtContext* SysYExtendedParser::ForStmtContext::stmt() {
  return getRuleContext<SysYExtendedParser::StmtContext>(0);
}


size_t SysYExtendedParser::ForStmtContext::getRuleIndex() const {
  return SysYExtendedParser::RuleForStmt;
}

void SysYExtendedParser::ForStmtContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterForStmt(this);
}

void SysYExtendedParser::ForStmtContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitForStmt(this);
}


antlrcpp::Any SysYExtendedParser::ForStmtContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitForStmt(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ForStmtContext* SysYExtendedParser::forStmt() {
  ForStmtContext *_localctx = _tracker.createInstance<ForStmtContext>(_ctx, getState());
  enterRule(_localctx, 64, SysYExtendedParser::RuleForStmt);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(382);
    match(SysYExtendedParser::FOR);
    setState(383);
    match(SysYExtendedParser::LPAREN);
    setState(384);
    forInit();
    setState(385);
    match(SysYExtendedParser::SEMICOLON);
    setState(386);
    cond();
    setState(387);
    match(SysYExtendedParser::SEMICOLON);
    setState(388);
    forUpdate();
    setState(389);
    match(SysYExtendedParser::RPAREN);
    setState(390);
    stmt();
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ForInitContext ------------------------------------------------------------------

SysYExtendedParser::ForInitContext::ForInitContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::VarDeclContext* SysYExtendedParser::ForInitContext::varDecl() {
  return getRuleContext<SysYExtendedParser::VarDeclContext>(0);
}

SysYExtendedParser::LValContext* SysYExtendedParser::ForInitContext::lVal() {
  return getRuleContext<SysYExtendedParser::LValContext>(0);
}

tree::TerminalNode* SysYExtendedParser::ForInitContext::ASSIGN() {
  return getToken(SysYExtendedParser::ASSIGN, 0);
}

SysYExtendedParser::ExpContext* SysYExtendedParser::ForInitContext::exp() {
  return getRuleContext<SysYExtendedParser::ExpContext>(0);
}


size_t SysYExtendedParser::ForInitContext::getRuleIndex() const {
  return SysYExtendedParser::RuleForInit;
}

void SysYExtendedParser::ForInitContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterForInit(this);
}

void SysYExtendedParser::ForInitContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitForInit(this);
}


antlrcpp::Any SysYExtendedParser::ForInitContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitForInit(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ForInitContext* SysYExtendedParser::forInit() {
  ForInitContext *_localctx = _tracker.createInstance<ForInitContext>(_ctx, getState());
  enterRule(_localctx, 66, SysYExtendedParser::RuleForInit);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(398);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::INT:
      case SysYExtendedParser::FLOAT: {
        enterOuterAlt(_localctx, 1);
        setState(392);
        varDecl();
        break;
      }

      case SysYExtendedParser::IDENT: {
        enterOuterAlt(_localctx, 2);
        setState(393);
        lVal();
        setState(394);
        match(SysYExtendedParser::ASSIGN);
        setState(395);
        exp();
        break;
      }

      case SysYExtendedParser::SEMICOLON: {
        enterOuterAlt(_localctx, 3);

        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ForUpdateContext ------------------------------------------------------------------

SysYExtendedParser::ForUpdateContext::ForUpdateContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

SysYExtendedParser::LValContext* SysYExtendedParser::ForUpdateContext::lVal() {
  return getRuleContext<SysYExtendedParser::LValContext>(0);
}

tree::TerminalNode* SysYExtendedParser::ForUpdateContext::ASSIGN() {
  return getToken(SysYExtendedParser::ASSIGN, 0);
}

SysYExtendedParser::ExpContext* SysYExtendedParser::ForUpdateContext::exp() {
  return getRuleContext<SysYExtendedParser::ExpContext>(0);
}


size_t SysYExtendedParser::ForUpdateContext::getRuleIndex() const {
  return SysYExtendedParser::RuleForUpdate;
}

void SysYExtendedParser::ForUpdateContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterForUpdate(this);
}

void SysYExtendedParser::ForUpdateContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitForUpdate(this);
}


antlrcpp::Any SysYExtendedParser::ForUpdateContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitForUpdate(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ForUpdateContext* SysYExtendedParser::forUpdate() {
  ForUpdateContext *_localctx = _tracker.createInstance<ForUpdateContext>(_ctx, getState());
  enterRule(_localctx, 68, SysYExtendedParser::RuleForUpdate);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    setState(405);
    _errHandler->sync(this);
    switch (_input->LA(1)) {
      case SysYExtendedParser::IDENT: {
        enterOuterAlt(_localctx, 1);
        setState(400);
        lVal();
        setState(401);
        match(SysYExtendedParser::ASSIGN);
        setState(402);
        exp();
        break;
      }

      case SysYExtendedParser::RPAREN: {
        enterOuterAlt(_localctx, 2);

        break;
      }

    default:
      throw NoViableAltException(this);
    }
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- ParallelForStmtContext ------------------------------------------------------------------

SysYExtendedParser::ParallelForStmtContext::ParallelForStmtContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::PRAGMA() {
  return getToken(SysYExtendedParser::PRAGMA, 0);
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::PARALLEL() {
  return getToken(SysYExtendedParser::PARALLEL, 0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ParallelForStmtContext::FOR() {
  return getTokens(SysYExtendedParser::FOR);
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::FOR(size_t i) {
  return getToken(SysYExtendedParser::FOR, i);
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::LPAREN() {
  return getToken(SysYExtendedParser::LPAREN, 0);
}

SysYExtendedParser::ForInitContext* SysYExtendedParser::ParallelForStmtContext::forInit() {
  return getRuleContext<SysYExtendedParser::ForInitContext>(0);
}

std::vector<tree::TerminalNode *> SysYExtendedParser::ParallelForStmtContext::SEMICOLON() {
  return getTokens(SysYExtendedParser::SEMICOLON);
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::SEMICOLON(size_t i) {
  return getToken(SysYExtendedParser::SEMICOLON, i);
}

SysYExtendedParser::CondContext* SysYExtendedParser::ParallelForStmtContext::cond() {
  return getRuleContext<SysYExtendedParser::CondContext>(0);
}

SysYExtendedParser::ForUpdateContext* SysYExtendedParser::ParallelForStmtContext::forUpdate() {
  return getRuleContext<SysYExtendedParser::ForUpdateContext>(0);
}

tree::TerminalNode* SysYExtendedParser::ParallelForStmtContext::RPAREN() {
  return getToken(SysYExtendedParser::RPAREN, 0);
}

SysYExtendedParser::StmtContext* SysYExtendedParser::ParallelForStmtContext::stmt() {
  return getRuleContext<SysYExtendedParser::StmtContext>(0);
}


size_t SysYExtendedParser::ParallelForStmtContext::getRuleIndex() const {
  return SysYExtendedParser::RuleParallelForStmt;
}

void SysYExtendedParser::ParallelForStmtContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterParallelForStmt(this);
}

void SysYExtendedParser::ParallelForStmtContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitParallelForStmt(this);
}


antlrcpp::Any SysYExtendedParser::ParallelForStmtContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitParallelForStmt(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::ParallelForStmtContext* SysYExtendedParser::parallelForStmt() {
  ParallelForStmtContext *_localctx = _tracker.createInstance<ParallelForStmtContext>(_ctx, getState());
  enterRule(_localctx, 70, SysYExtendedParser::RuleParallelForStmt);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(407);
    match(SysYExtendedParser::PRAGMA);
    setState(408);
    match(SysYExtendedParser::PARALLEL);
    setState(409);
    match(SysYExtendedParser::FOR);
    setState(410);
    match(SysYExtendedParser::FOR);
    setState(411);
    match(SysYExtendedParser::LPAREN);
    setState(412);
    forInit();
    setState(413);
    match(SysYExtendedParser::SEMICOLON);
    setState(414);
    cond();
    setState(415);
    match(SysYExtendedParser::SEMICOLON);
    setState(416);
    forUpdate();
    setState(417);
    match(SysYExtendedParser::RPAREN);
    setState(418);
    stmt();
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

//----------------- BarrierStmtContext ------------------------------------------------------------------

SysYExtendedParser::BarrierStmtContext::BarrierStmtContext(ParserRuleContext *parent, size_t invokingState)
  : ParserRuleContext(parent, invokingState) {
}

tree::TerminalNode* SysYExtendedParser::BarrierStmtContext::BARRIER() {
  return getToken(SysYExtendedParser::BARRIER, 0);
}

tree::TerminalNode* SysYExtendedParser::BarrierStmtContext::SEMICOLON() {
  return getToken(SysYExtendedParser::SEMICOLON, 0);
}


size_t SysYExtendedParser::BarrierStmtContext::getRuleIndex() const {
  return SysYExtendedParser::RuleBarrierStmt;
}

void SysYExtendedParser::BarrierStmtContext::enterRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->enterBarrierStmt(this);
}

void SysYExtendedParser::BarrierStmtContext::exitRule(tree::ParseTreeListener *listener) {
  auto parserListener = dynamic_cast<SysYExtendedListener *>(listener);
  if (parserListener != nullptr)
    parserListener->exitBarrierStmt(this);
}


antlrcpp::Any SysYExtendedParser::BarrierStmtContext::accept(tree::ParseTreeVisitor *visitor) {
  if (auto parserVisitor = dynamic_cast<SysYExtendedVisitor*>(visitor))
    return parserVisitor->visitBarrierStmt(this);
  else
    return visitor->visitChildren(this);
}

SysYExtendedParser::BarrierStmtContext* SysYExtendedParser::barrierStmt() {
  BarrierStmtContext *_localctx = _tracker.createInstance<BarrierStmtContext>(_ctx, getState());
  enterRule(_localctx, 72, SysYExtendedParser::RuleBarrierStmt);

  auto onExit = finally([=] {
    exitRule();
  });
  try {
    enterOuterAlt(_localctx, 1);
    setState(420);
    match(SysYExtendedParser::BARRIER);
    setState(421);
    match(SysYExtendedParser::SEMICOLON);
   
  }
  catch (RecognitionException &e) {
    _errHandler->reportError(this, e);
    _localctx->exception = std::current_exception();
    _errHandler->recover(this, _localctx->exception);
  }

  return _localctx;
}

bool SysYExtendedParser::sempred(RuleContext *context, size_t ruleIndex, size_t predicateIndex) {
  switch (ruleIndex) {
    case 25: return mulExpSempred(dynamic_cast<MulExpContext *>(context), predicateIndex);
    case 26: return addExpSempred(dynamic_cast<AddExpContext *>(context), predicateIndex);
    case 27: return relExpSempred(dynamic_cast<RelExpContext *>(context), predicateIndex);
    case 28: return eqExpSempred(dynamic_cast<EqExpContext *>(context), predicateIndex);
    case 29: return lAndExpSempred(dynamic_cast<LAndExpContext *>(context), predicateIndex);
    case 30: return lOrExpSempred(dynamic_cast<LOrExpContext *>(context), predicateIndex);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::mulExpSempred(MulExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 0: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::addExpSempred(AddExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 1: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::relExpSempred(RelExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 2: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::eqExpSempred(EqExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 3: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::lAndExpSempred(LAndExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 4: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

bool SysYExtendedParser::lOrExpSempred(LOrExpContext *_localctx, size_t predicateIndex) {
  switch (predicateIndex) {
    case 5: return precpred(_ctx, 1);

  default:
    break;
  }
  return true;
}

// Static vars and initialization.
std::vector<dfa::DFA> SysYExtendedParser::_decisionToDFA;
atn::PredictionContextCache SysYExtendedParser::_sharedContextCache;

// We own the ATN which in turn owns the ATN states.
atn::ATN SysYExtendedParser::_atn;
std::vector<uint16_t> SysYExtendedParser::_serializedATN;

std::vector<std::string> SysYExtendedParser::_ruleNames = {
  "program", "compUnit", "decl", "constDecl", "bType", "constDef", "constInitVal", 
  "varDecl", "varDef", "initVal", "funcDef", "funcType", "funcFormalParams", 
  "funcFormalParam", "block", "blockItem", "stmt", "exp", "cond", "lVal", 
  "primaryExp", "number", "unaryExp", "unaryOP", "funcRealParams", "mulExp", 
  "addExp", "relExp", "eqExp", "lAndExp", "lOrExp", "constExp", "forStmt", 
  "forInit", "forUpdate", "parallelForStmt", "barrierStmt"
};

std::vector<std::string> SysYExtendedParser::_literalNames = {
  "", "'int'", "'float'", "'void'", "'const'", "'if'", "'else'", "'while'", 
  "'for'", "'break'", "'continue'", "'return'", "'#pragma'", "'parallel'", 
  "'barrier'", "';'", "','", "'('", "')'", "'{'", "'}'", "'['", "']'", "'='", 
  "'+'", "'-'", "'*'", "'/'", "'%'", "'<'", "'>'", "'<='", "'>='", "'=='", 
  "'!='", "'&&'", "'||'", "'!'"
};

std::vector<std::string> SysYExtendedParser::_symbolicNames = {
  "", "INT", "FLOAT", "VOID", "CONST", "IF", "ELSE", "WHILE", "FOR", "BREAK", 
  "CONTINUE", "RETURN", "PRAGMA", "PARALLEL", "BARRIER", "SEMICOLON", "COMMA", 
  "LPAREN", "RPAREN", "LBRACE", "RBRACE", "LBRACKET", "RBRACKET", "ASSIGN", 
  "ADD", "SUB", "MUL", "DIV", "MOD", "LT", "GT", "LE", "GE", "EQ", "NE", 
  "AND", "OR", "NOT", "IDENT", "WHIESPACE", "LINECOMMENT", "BLOCKCOMMENT", 
  "INTCONST", "FLOATCONST"
};

dfa::Vocabulary SysYExtendedParser::_vocabulary(_literalNames, _symbolicNames);

std::vector<std::string> SysYExtendedParser::_tokenNames;

SysYExtendedParser::Initializer::Initializer() {
	for (size_t i = 0; i < _symbolicNames.size(); ++i) {
		std::string name = _vocabulary.getLiteralName(i);
		if (name.empty()) {
			name = _vocabulary.getSymbolicName(i);
		}

		if (name.empty()) {
			_tokenNames.push_back("<INVALID>");
		} else {
      _tokenNames.push_back(name);
    }
	}

  _serializedATN = {
    0x3, 0x608b, 0xa72a, 0x8133, 0xb9ed, 0x417c, 0x3be7, 0x7786, 0x5964, 
    0x3, 0x2d, 0x1aa, 0x4, 0x2, 0x9, 0x2, 0x4, 0x3, 0x9, 0x3, 0x4, 0x4, 
    0x9, 0x4, 0x4, 0x5, 0x9, 0x5, 0x4, 0x6, 0x9, 0x6, 0x4, 0x7, 0x9, 0x7, 
    0x4, 0x8, 0x9, 0x8, 0x4, 0x9, 0x9, 0x9, 0x4, 0xa, 0x9, 0xa, 0x4, 0xb, 
    0x9, 0xb, 0x4, 0xc, 0x9, 0xc, 0x4, 0xd, 0x9, 0xd, 0x4, 0xe, 0x9, 0xe, 
    0x4, 0xf, 0x9, 0xf, 0x4, 0x10, 0x9, 0x10, 0x4, 0x11, 0x9, 0x11, 0x4, 
    0x12, 0x9, 0x12, 0x4, 0x13, 0x9, 0x13, 0x4, 0x14, 0x9, 0x14, 0x4, 0x15, 
    0x9, 0x15, 0x4, 0x16, 0x9, 0x16, 0x4, 0x17, 0x9, 0x17, 0x4, 0x18, 0x9, 
    0x18, 0x4, 0x19, 0x9, 0x19, 0x4, 0x1a, 0x9, 0x1a, 0x4, 0x1b, 0x9, 0x1b, 
    0x4, 0x1c, 0x9, 0x1c, 0x4, 0x1d, 0x9, 0x1d, 0x4, 0x1e, 0x9, 0x1e, 0x4, 
    0x1f, 0x9, 0x1f, 0x4, 0x20, 0x9, 0x20, 0x4, 0x21, 0x9, 0x21, 0x4, 0x22, 
    0x9, 0x22, 0x4, 0x23, 0x9, 0x23, 0x4, 0x24, 0x9, 0x24, 0x4, 0x25, 0x9, 
    0x25, 0x4, 0x26, 0x9, 0x26, 0x3, 0x2, 0x6, 0x2, 0x4e, 0xa, 0x2, 0xd, 
    0x2, 0xe, 0x2, 0x4f, 0x3, 0x2, 0x3, 0x2, 0x3, 0x3, 0x3, 0x3, 0x5, 0x3, 
    0x56, 0xa, 0x3, 0x3, 0x4, 0x3, 0x4, 0x5, 0x4, 0x5a, 0xa, 0x4, 0x3, 0x5, 
    0x3, 0x5, 0x3, 0x5, 0x3, 0x5, 0x3, 0x5, 0x7, 0x5, 0x61, 0xa, 0x5, 0xc, 
    0x5, 0xe, 0x5, 0x64, 0xb, 0x5, 0x3, 0x5, 0x3, 0x5, 0x3, 0x6, 0x3, 0x6, 
    0x3, 0x7, 0x3, 0x7, 0x3, 0x7, 0x3, 0x7, 0x3, 0x7, 0x7, 0x7, 0x6f, 0xa, 
    0x7, 0xc, 0x7, 0xe, 0x7, 0x72, 0xb, 0x7, 0x3, 0x7, 0x3, 0x7, 0x3, 0x7, 
    0x3, 0x8, 0x3, 0x8, 0x3, 0x8, 0x3, 0x8, 0x3, 0x8, 0x7, 0x8, 0x7c, 0xa, 
    0x8, 0xc, 0x8, 0xe, 0x8, 0x7f, 0xb, 0x8, 0x5, 0x8, 0x81, 0xa, 0x8, 0x3, 
    0x8, 0x5, 0x8, 0x84, 0xa, 0x8, 0x3, 0x9, 0x3, 0x9, 0x3, 0x9, 0x3, 0x9, 
    0x7, 0x9, 0x8a, 0xa, 0x9, 0xc, 0x9, 0xe, 0x9, 0x8d, 0xb, 0x9, 0x3, 0x9, 
    0x3, 0x9, 0x3, 0xa, 0x3, 0xa, 0x3, 0xa, 0x3, 0xa, 0x3, 0xa, 0x7, 0xa, 
    0x96, 0xa, 0xa, 0xc, 0xa, 0xe, 0xa, 0x99, 0xb, 0xa, 0x3, 0xa, 0x3, 0xa, 
    0x3, 0xa, 0x3, 0xa, 0x3, 0xa, 0x7, 0xa, 0xa0, 0xa, 0xa, 0xc, 0xa, 0xe, 
    0xa, 0xa3, 0xb, 0xa, 0x3, 0xa, 0x3, 0xa, 0x5, 0xa, 0xa7, 0xa, 0xa, 0x3, 
    0xb, 0x3, 0xb, 0x3, 0xb, 0x3, 0xb, 0x3, 0xb, 0x7, 0xb, 0xae, 0xa, 0xb, 
    0xc, 0xb, 0xe, 0xb, 0xb1, 0xb, 0xb, 0x5, 0xb, 0xb3, 0xa, 0xb, 0x3, 0xb, 
    0x5, 0xb, 0xb6, 0xa, 0xb, 0x3, 0xc, 0x3, 0xc, 0x3, 0xc, 0x3, 0xc, 0x5, 
    0xc, 0xbc, 0xa, 0xc, 0x3, 0xc, 0x3, 0xc, 0x3, 0xc, 0x3, 0xd, 0x3, 0xd, 
    0x5, 0xd, 0xc3, 0xa, 0xd, 0x3, 0xe, 0x3, 0xe, 0x3, 0xe, 0x7, 0xe, 0xc8, 
    0xa, 0xe, 0xc, 0xe, 0xe, 0xe, 0xcb, 0xb, 0xe, 0x3, 0xf, 0x3, 0xf, 0x3, 
    0xf, 0x3, 0xf, 0x3, 0xf, 0x3, 0xf, 0x3, 0xf, 0x3, 0xf, 0x7, 0xf, 0xd5, 
    0xa, 0xf, 0xc, 0xf, 0xe, 0xf, 0xd8, 0xb, 0xf, 0x5, 0xf, 0xda, 0xa, 0xf, 
    0x3, 0x10, 0x3, 0x10, 0x7, 0x10, 0xde, 0xa, 0x10, 0xc, 0x10, 0xe, 0x10, 
    0xe1, 0xb, 0x10, 0x3, 0x10, 0x3, 0x10, 0x3, 0x11, 0x3, 0x11, 0x5, 0x11, 
    0xe7, 0xa, 0x11, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 
    0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 
    0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x5, 0x12, 0xf9, 0xa, 
    0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 
    0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 0x12, 0x3, 
    0x12, 0x3, 0x12, 0x3, 0x12, 0x5, 0x12, 0x10a, 0xa, 0x12, 0x3, 0x12, 
    0x5, 0x12, 0x10d, 0xa, 0x12, 0x3, 0x13, 0x3, 0x13, 0x3, 0x14, 0x3, 0x14, 
    0x3, 0x15, 0x3, 0x15, 0x3, 0x15, 0x3, 0x15, 0x3, 0x15, 0x7, 0x15, 0x118, 
    0xa, 0x15, 0xc, 0x15, 0xe, 0x15, 0x11b, 0xb, 0x15, 0x3, 0x16, 0x3, 0x16, 
    0x3, 0x16, 0x3, 0x16, 0x3, 0x16, 0x3, 0x16, 0x5, 0x16, 0x123, 0xa, 0x16, 
    0x3, 0x17, 0x3, 0x17, 0x3, 0x18, 0x3, 0x18, 0x3, 0x18, 0x3, 0x18, 0x5, 
    0x18, 0x12b, 0xa, 0x18, 0x3, 0x18, 0x3, 0x18, 0x3, 0x18, 0x3, 0x18, 
    0x5, 0x18, 0x131, 0xa, 0x18, 0x3, 0x19, 0x3, 0x19, 0x3, 0x1a, 0x3, 0x1a, 
    0x3, 0x1a, 0x7, 0x1a, 0x138, 0xa, 0x1a, 0xc, 0x1a, 0xe, 0x1a, 0x13b, 
    0xb, 0x1a, 0x3, 0x1b, 0x3, 0x1b, 0x3, 0x1b, 0x3, 0x1b, 0x3, 0x1b, 0x3, 
    0x1b, 0x7, 0x1b, 0x143, 0xa, 0x1b, 0xc, 0x1b, 0xe, 0x1b, 0x146, 0xb, 
    0x1b, 0x3, 0x1c, 0x3, 0x1c, 0x3, 0x1c, 0x3, 0x1c, 0x3, 0x1c, 0x3, 0x1c, 
    0x7, 0x1c, 0x14e, 0xa, 0x1c, 0xc, 0x1c, 0xe, 0x1c, 0x151, 0xb, 0x1c, 
    0x3, 0x1d, 0x3, 0x1d, 0x3, 0x1d, 0x3, 0x1d, 0x3, 0x1d, 0x3, 0x1d, 0x7, 
    0x1d, 0x159, 0xa, 0x1d, 0xc, 0x1d, 0xe, 0x1d, 0x15c, 0xb, 0x1d, 0x3, 
    0x1e, 0x3, 0x1e, 0x3, 0x1e, 0x3, 0x1e, 0x3, 0x1e, 0x3, 0x1e, 0x7, 0x1e, 
    0x164, 0xa, 0x1e, 0xc, 0x1e, 0xe, 0x1e, 0x167, 0xb, 0x1e, 0x3, 0x1f, 
    0x3, 0x1f, 0x3, 0x1f, 0x3, 0x1f, 0x3, 0x1f, 0x3, 0x1f, 0x7, 0x1f, 0x16f, 
    0xa, 0x1f, 0xc, 0x1f, 0xe, 0x1f, 0x172, 0xb, 0x1f, 0x3, 0x20, 0x3, 0x20, 
    0x3, 0x20, 0x3, 0x20, 0x3, 0x20, 0x3, 0x20, 0x7, 0x20, 0x17a, 0xa, 0x20, 
    0xc, 0x20, 0xe, 0x20, 0x17d, 0xb, 0x20, 0x3, 0x21, 0x3, 0x21, 0x3, 0x22, 
    0x3, 0x22, 0x3, 0x22, 0x3, 0x22, 0x3, 0x22, 0x3, 0x22, 0x3, 0x22, 0x3, 
    0x22, 0x3, 0x22, 0x3, 0x22, 0x3, 0x23, 0x3, 0x23, 0x3, 0x23, 0x3, 0x23, 
    0x3, 0x23, 0x3, 0x23, 0x5, 0x23, 0x191, 0xa, 0x23, 0x3, 0x24, 0x3, 0x24, 
    0x3, 0x24, 0x3, 0x24, 0x3, 0x24, 0x5, 0x24, 0x198, 0xa, 0x24, 0x3, 0x25, 
    0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 
    0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x25, 0x3, 0x26, 
    0x3, 0x26, 0x3, 0x26, 0x3, 0x26, 0x2, 0x8, 0x34, 0x36, 0x38, 0x3a, 0x3c, 
    0x3e, 0x27, 0x2, 0x4, 0x6, 0x8, 0xa, 0xc, 0xe, 0x10, 0x12, 0x14, 0x16, 
    0x18, 0x1a, 0x1c, 0x1e, 0x20, 0x22, 0x24, 0x26, 0x28, 0x2a, 0x2c, 0x2e, 
    0x30, 0x32, 0x34, 0x36, 0x38, 0x3a, 0x3c, 0x3e, 0x40, 0x42, 0x44, 0x46, 
    0x48, 0x4a, 0x2, 0x9, 0x3, 0x2, 0x3, 0x4, 0x3, 0x2, 0x2c, 0x2d, 0x4, 
    0x2, 0x1a, 0x1b, 0x27, 0x27, 0x3, 0x2, 0x1c, 0x1e, 0x3, 0x2, 0x1a, 0x1b, 
    0x3, 0x2, 0x1f, 0x22, 0x3, 0x2, 0x23, 0x24, 0x2, 0x1b6, 0x2, 0x4d, 0x3, 
    0x2, 0x2, 0x2, 0x4, 0x55, 0x3, 0x2, 0x2, 0x2, 0x6, 0x59, 0x3, 0x2, 0x2, 
    0x2, 0x8, 0x5b, 0x3, 0x2, 0x2, 0x2, 0xa, 0x67, 0x3, 0x2, 0x2, 0x2, 0xc, 
    0x69, 0x3, 0x2, 0x2, 0x2, 0xe, 0x83, 0x3, 0x2, 0x2, 0x2, 0x10, 0x85, 
    0x3, 0x2, 0x2, 0x2, 0x12, 0xa6, 0x3, 0x2, 0x2, 0x2, 0x14, 0xb5, 0x3, 
    0x2, 0x2, 0x2, 0x16, 0xb7, 0x3, 0x2, 0x2, 0x2, 0x18, 0xc2, 0x3, 0x2, 
    0x2, 0x2, 0x1a, 0xc4, 0x3, 0x2, 0x2, 0x2, 0x1c, 0xcc, 0x3, 0x2, 0x2, 
    0x2, 0x1e, 0xdb, 0x3, 0x2, 0x2, 0x2, 0x20, 0xe6, 0x3, 0x2, 0x2, 0x2, 
    0x22, 0x10c, 0x3, 0x2, 0x2, 0x2, 0x24, 0x10e, 0x3, 0x2, 0x2, 0x2, 0x26, 
    0x110, 0x3, 0x2, 0x2, 0x2, 0x28, 0x112, 0x3, 0x2, 0x2, 0x2, 0x2a, 0x122, 
    0x3, 0x2, 0x2, 0x2, 0x2c, 0x124, 0x3, 0x2, 0x2, 0x2, 0x2e, 0x130, 0x3, 
    0x2, 0x2, 0x2, 0x30, 0x132, 0x3, 0x2, 0x2, 0x2, 0x32, 0x134, 0x3, 0x2, 
    0x2, 0x2, 0x34, 0x13c, 0x3, 0x2, 0x2, 0x2, 0x36, 0x147, 0x3, 0x2, 0x2, 
    0x2, 0x38, 0x152, 0x3, 0x2, 0x2, 0x2, 0x3a, 0x15d, 0x3, 0x2, 0x2, 0x2, 
    0x3c, 0x168, 0x3, 0x2, 0x2, 0x2, 0x3e, 0x173, 0x3, 0x2, 0x2, 0x2, 0x40, 
    0x17e, 0x3, 0x2, 0x2, 0x2, 0x42, 0x180, 0x3, 0x2, 0x2, 0x2, 0x44, 0x190, 
    0x3, 0x2, 0x2, 0x2, 0x46, 0x197, 0x3, 0x2, 0x2, 0x2, 0x48, 0x199, 0x3, 
    0x2, 0x2, 0x2, 0x4a, 0x1a6, 0x3, 0x2, 0x2, 0x2, 0x4c, 0x4e, 0x5, 0x4, 
    0x3, 0x2, 0x4d, 0x4c, 0x3, 0x2, 0x2, 0x2, 0x4e, 0x4f, 0x3, 0x2, 0x2, 
    0x2, 0x4f, 0x4d, 0x3, 0x2, 0x2, 0x2, 0x4f, 0x50, 0x3, 0x2, 0x2, 0x2, 
    0x50, 0x51, 0x3, 0x2, 0x2, 0x2, 0x51, 0x52, 0x7, 0x2, 0x2, 0x3, 0x52, 
    0x3, 0x3, 0x2, 0x2, 0x2, 0x53, 0x56, 0x5, 0x6, 0x4, 0x2, 0x54, 0x56, 
    0x5, 0x16, 0xc, 0x2, 0x55, 0x53, 0x3, 0x2, 0x2, 0x2, 0x55, 0x54, 0x3, 
    0x2, 0x2, 0x2, 0x56, 0x5, 0x3, 0x2, 0x2, 0x2, 0x57, 0x5a, 0x5, 0x8, 
    0x5, 0x2, 0x58, 0x5a, 0x5, 0x10, 0x9, 0x2, 0x59, 0x57, 0x3, 0x2, 0x2, 
    0x2, 0x59, 0x58, 0x3, 0x2, 0x2, 0x2, 0x5a, 0x7, 0x3, 0x2, 0x2, 0x2, 
    0x5b, 0x5c, 0x7, 0x6, 0x2, 0x2, 0x5c, 0x5d, 0x5, 0xa, 0x6, 0x2, 0x5d, 
    0x62, 0x5, 0xc, 0x7, 0x2, 0x5e, 0x5f, 0x7, 0x12, 0x2, 0x2, 0x5f, 0x61, 
    0x5, 0xc, 0x7, 0x2, 0x60, 0x5e, 0x3, 0x2, 0x2, 0x2, 0x61, 0x64, 0x3, 
    0x2, 0x2, 0x2, 0x62, 0x60, 0x3, 0x2, 0x2, 0x2, 0x62, 0x63, 0x3, 0x2, 
    0x2, 0x2, 0x63, 0x65, 0x3, 0x2, 0x2, 0x2, 0x64, 0x62, 0x3, 0x2, 0x2, 
    0x2, 0x65, 0x66, 0x7, 0x11, 0x2, 0x2, 0x66, 0x9, 0x3, 0x2, 0x2, 0x2, 
    0x67, 0x68, 0x9, 0x2, 0x2, 0x2, 0x68, 0xb, 0x3, 0x2, 0x2, 0x2, 0x69, 
    0x70, 0x7, 0x28, 0x2, 0x2, 0x6a, 0x6b, 0x7, 0x17, 0x2, 0x2, 0x6b, 0x6c, 
    0x5, 0x40, 0x21, 0x2, 0x6c, 0x6d, 0x7, 0x18, 0x2, 0x2, 0x6d, 0x6f, 0x3, 
    0x2, 0x2, 0x2, 0x6e, 0x6a, 0x3, 0x2, 0x2, 0x2, 0x6f, 0x72, 0x3, 0x2, 
    0x2, 0x2, 0x70, 0x6e, 0x3, 0x2, 0x2, 0x2, 0x70, 0x71, 0x3, 0x2, 0x2, 
    0x2, 0x71, 0x73, 0x3, 0x2, 0x2, 0x2, 0x72, 0x70, 0x3, 0x2, 0x2, 0x2, 
    0x73, 0x74, 0x7, 0x19, 0x2, 0x2, 0x74, 0x75, 0x5, 0xe, 0x8, 0x2, 0x75, 
    0xd, 0x3, 0x2, 0x2, 0x2, 0x76, 0x84, 0x5, 0x40, 0x21, 0x2, 0x77, 0x80, 
    0x7, 0x15, 0x2, 0x2, 0x78, 0x7d, 0x5, 0xe, 0x8, 0x2, 0x79, 0x7a, 0x7, 
    0x12, 0x2, 0x2, 0x7a, 0x7c, 0x5, 0xe, 0x8, 0x2, 0x7b, 0x79, 0x3, 0x2, 
    0x2, 0x2, 0x7c, 0x7f, 0x3, 0x2, 0x2, 0x2, 0x7d, 0x7b, 0x3, 0x2, 0x2, 
    0x2, 0x7d, 0x7e, 0x3, 0x2, 0x2, 0x2, 0x7e, 0x81, 0x3, 0x2, 0x2, 0x2, 
    0x7f, 0x7d, 0x3, 0x2, 0x2, 0x2, 0x80, 0x78, 0x3, 0x2, 0x2, 0x2, 0x80, 
    0x81, 0x3, 0x2, 0x2, 0x2, 0x81, 0x82, 0x3, 0x2, 0x2, 0x2, 0x82, 0x84, 
    0x7, 0x16, 0x2, 0x2, 0x83, 0x76, 0x3, 0x2, 0x2, 0x2, 0x83, 0x77, 0x3, 
    0x2, 0x2, 0x2, 0x84, 0xf, 0x3, 0x2, 0x2, 0x2, 0x85, 0x86, 0x5, 0xa, 
    0x6, 0x2, 0x86, 0x8b, 0x5, 0x12, 0xa, 0x2, 0x87, 0x88, 0x7, 0x12, 0x2, 
    0x2, 0x88, 0x8a, 0x5, 0x12, 0xa, 0x2, 0x89, 0x87, 0x3, 0x2, 0x2, 0x2, 
    0x8a, 0x8d, 0x3, 0x2, 0x2, 0x2, 0x8b, 0x89, 0x3, 0x2, 0x2, 0x2, 0x8b, 
    0x8c, 0x3, 0x2, 0x2, 0x2, 0x8c, 0x8e, 0x3, 0x2, 0x2, 0x2, 0x8d, 0x8b, 
    0x3, 0x2, 0x2, 0x2, 0x8e, 0x8f, 0x7, 0x11, 0x2, 0x2, 0x8f, 0x11, 0x3, 
    0x2, 0x2, 0x2, 0x90, 0x97, 0x7, 0x28, 0x2, 0x2, 0x91, 0x92, 0x7, 0x17, 
    0x2, 0x2, 0x92, 0x93, 0x5, 0x40, 0x21, 0x2, 0x93, 0x94, 0x7, 0x18, 0x2, 
    0x2, 0x94, 0x96, 0x3, 0x2, 0x2, 0x2, 0x95, 0x91, 0x3, 0x2, 0x2, 0x2, 
    0x96, 0x99, 0x3, 0x2, 0x2, 0x2, 0x97, 0x95, 0x3, 0x2, 0x2, 0x2, 0x97, 
    0x98, 0x3, 0x2, 0x2, 0x2, 0x98, 0xa7, 0x3, 0x2, 0x2, 0x2, 0x99, 0x97, 
    0x3, 0x2, 0x2, 0x2, 0x9a, 0xa1, 0x7, 0x28, 0x2, 0x2, 0x9b, 0x9c, 0x7, 
    0x17, 0x2, 0x2, 0x9c, 0x9d, 0x5, 0x40, 0x21, 0x2, 0x9d, 0x9e, 0x7, 0x18, 
    0x2, 0x2, 0x9e, 0xa0, 0x3, 0x2, 0x2, 0x2, 0x9f, 0x9b, 0x3, 0x2, 0x2, 
    0x2, 0xa0, 0xa3, 0x3, 0x2, 0x2, 0x2, 0xa1, 0x9f, 0x3, 0x2, 0x2, 0x2, 
    0xa1, 0xa2, 0x3, 0x2, 0x2, 0x2, 0xa2, 0xa4, 0x3, 0x2, 0x2, 0x2, 0xa3, 
    0xa1, 0x3, 0x2, 0x2, 0x2, 0xa4, 0xa5, 0x7, 0x19, 0x2, 0x2, 0xa5, 0xa7, 
    0x5, 0x14, 0xb, 0x2, 0xa6, 0x90, 0x3, 0x2, 0x2, 0x2, 0xa6, 0x9a, 0x3, 
    0x2, 0x2, 0x2, 0xa7, 0x13, 0x3, 0x2, 0x2, 0x2, 0xa8, 0xb6, 0x5, 0x24, 
    0x13, 0x2, 0xa9, 0xb2, 0x7, 0x15, 0x2, 0x2, 0xaa, 0xaf, 0x5, 0x14, 0xb, 
    0x2, 0xab, 0xac, 0x7, 0x12, 0x2, 0x2, 0xac, 0xae, 0x5, 0x14, 0xb, 0x2, 
    0xad, 0xab, 0x3, 0x2, 0x2, 0x2, 0xae, 0xb1, 0x3, 0x2, 0x2, 0x2, 0xaf, 
    0xad, 0x3, 0x2, 0x2, 0x2, 0xaf, 0xb0, 0x3, 0x2, 0x2, 0x2, 0xb0, 0xb3, 
    0x3, 0x2, 0x2, 0x2, 0xb1, 0xaf, 0x3, 0x2, 0x2, 0x2, 0xb2, 0xaa, 0x3, 
    0x2, 0x2, 0x2, 0xb2, 0xb3, 0x3, 0x2, 0x2, 0x2, 0xb3, 0xb4, 0x3, 0x2, 
    0x2, 0x2, 0xb4, 0xb6, 0x7, 0x16, 0x2, 0x2, 0xb5, 0xa8, 0x3, 0x2, 0x2, 
    0x2, 0xb5, 0xa9, 0x3, 0x2, 0x2, 0x2, 0xb6, 0x15, 0x3, 0x2, 0x2, 0x2, 
    0xb7, 0xb8, 0x5, 0x18, 0xd, 0x2, 0xb8, 0xb9, 0x7, 0x28, 0x2, 0x2, 0xb9, 
    0xbb, 0x7, 0x13, 0x2, 0x2, 0xba, 0xbc, 0x5, 0x1a, 0xe, 0x2, 0xbb, 0xba, 
    0x3, 0x2, 0x2, 0x2, 0xbb, 0xbc, 0x3, 0x2, 0x2, 0x2, 0xbc, 0xbd, 0x3, 
    0x2, 0x2, 0x2, 0xbd, 0xbe, 0x7, 0x14, 0x2, 0x2, 0xbe, 0xbf, 0x5, 0x1e, 
    0x10, 0x2, 0xbf, 0x17, 0x3, 0x2, 0x2, 0x2, 0xc0, 0xc3, 0x7, 0x5, 0x2, 
    0x2, 0xc1, 0xc3, 0x5, 0xa, 0x6, 0x2, 0xc2, 0xc0, 0x3, 0x2, 0x2, 0x2, 
    0xc2, 0xc1, 0x3, 0x2, 0x2, 0x2, 0xc3, 0x19, 0x3, 0x2, 0x2, 0x2, 0xc4, 
    0xc9, 0x5, 0x1c, 0xf, 0x2, 0xc5, 0xc6, 0x7, 0x12, 0x2, 0x2, 0xc6, 0xc8, 
    0x5, 0x1c, 0xf, 0x2, 0xc7, 0xc5, 0x3, 0x2, 0x2, 0x2, 0xc8, 0xcb, 0x3, 
    0x2, 0x2, 0x2, 0xc9, 0xc7, 0x3, 0x2, 0x2, 0x2, 0xc9, 0xca, 0x3, 0x2, 
    0x2, 0x2, 0xca, 0x1b, 0x3, 0x2, 0x2, 0x2, 0xcb, 0xc9, 0x3, 0x2, 0x2, 
    0x2, 0xcc, 0xcd, 0x5, 0xa, 0x6, 0x2, 0xcd, 0xd9, 0x7, 0x28, 0x2, 0x2, 
    0xce, 0xcf, 0x7, 0x17, 0x2, 0x2, 0xcf, 0xd6, 0x7, 0x18, 0x2, 0x2, 0xd0, 
    0xd1, 0x7, 0x17, 0x2, 0x2, 0xd1, 0xd2, 0x5, 0x24, 0x13, 0x2, 0xd2, 0xd3, 
    0x7, 0x18, 0x2, 0x2, 0xd3, 0xd5, 0x3, 0x2, 0x2, 0x2, 0xd4, 0xd0, 0x3, 
    0x2, 0x2, 0x2, 0xd5, 0xd8, 0x3, 0x2, 0x2, 0x2, 0xd6, 0xd4, 0x3, 0x2, 
    0x2, 0x2, 0xd6, 0xd7, 0x3, 0x2, 0x2, 0x2, 0xd7, 0xda, 0x3, 0x2, 0x2, 
    0x2, 0xd8, 0xd6, 0x3, 0x2, 0x2, 0x2, 0xd9, 0xce, 0x3, 0x2, 0x2, 0x2, 
    0xd9, 0xda, 0x3, 0x2, 0x2, 0x2, 0xda, 0x1d, 0x3, 0x2, 0x2, 0x2, 0xdb, 
    0xdf, 0x7, 0x15, 0x2, 0x2, 0xdc, 0xde, 0x5, 0x20, 0x11, 0x2, 0xdd, 0xdc, 
    0x3, 0x2, 0x2, 0x2, 0xde, 0xe1, 0x3, 0x2, 0x2, 0x2, 0xdf, 0xdd, 0x3, 
    0x2, 0x2, 0x2, 0xdf, 0xe0, 0x3, 0x2, 0x2, 0x2, 0xe0, 0xe2, 0x3, 0x2, 
    0x2, 0x2, 0xe1, 0xdf, 0x3, 0x2, 0x2, 0x2, 0xe2, 0xe3, 0x7, 0x16, 0x2, 
    0x2, 0xe3, 0x1f, 0x3, 0x2, 0x2, 0x2, 0xe4, 0xe7, 0x5, 0x6, 0x4, 0x2, 
    0xe5, 0xe7, 0x5, 0x22, 0x12, 0x2, 0xe6, 0xe4, 0x3, 0x2, 0x2, 0x2, 0xe6, 
    0xe5, 0x3, 0x2, 0x2, 0x2, 0xe7, 0x21, 0x3, 0x2, 0x2, 0x2, 0xe8, 0xe9, 
    0x5, 0x28, 0x15, 0x2, 0xe9, 0xea, 0x7, 0x19, 0x2, 0x2, 0xea, 0xeb, 0x5, 
    0x24, 0x13, 0x2, 0xeb, 0xec, 0x7, 0x11, 0x2, 0x2, 0xec, 0x10d, 0x3, 
    0x2, 0x2, 0x2, 0xed, 0xee, 0x5, 0x24, 0x13, 0x2, 0xee, 0xef, 0x7, 0x11, 
    0x2, 0x2, 0xef, 0x10d, 0x3, 0x2, 0x2, 0x2, 0xf0, 0x10d, 0x5, 0x1e, 0x10, 
    0x2, 0xf1, 0xf2, 0x7, 0x7, 0x2, 0x2, 0xf2, 0xf3, 0x7, 0x13, 0x2, 0x2, 
    0xf3, 0xf4, 0x5, 0x26, 0x14, 0x2, 0xf4, 0xf5, 0x7, 0x14, 0x2, 0x2, 0xf5, 
    0xf8, 0x5, 0x22, 0x12, 0x2, 0xf6, 0xf7, 0x7, 0x8, 0x2, 0x2, 0xf7, 0xf9, 
    0x5, 0x22, 0x12, 0x2, 0xf8, 0xf6, 0x3, 0x2, 0x2, 0x2, 0xf8, 0xf9, 0x3, 
    0x2, 0x2, 0x2, 0xf9, 0x10d, 0x3, 0x2, 0x2, 0x2, 0xfa, 0xfb, 0x7, 0x9, 
    0x2, 0x2, 0xfb, 0xfc, 0x7, 0x13, 0x2, 0x2, 0xfc, 0xfd, 0x5, 0x26, 0x14, 
    0x2, 0xfd, 0xfe, 0x7, 0x14, 0x2, 0x2, 0xfe, 0xff, 0x5, 0x22, 0x12, 0x2, 
    0xff, 0x10d, 0x3, 0x2, 0x2, 0x2, 0x100, 0x10d, 0x5, 0x42, 0x22, 0x2, 
    0x101, 0x10d, 0x5, 0x48, 0x25, 0x2, 0x102, 0x10d, 0x5, 0x4a, 0x26, 0x2, 
    0x103, 0x104, 0x7, 0xb, 0x2, 0x2, 0x104, 0x10d, 0x7, 0x11, 0x2, 0x2, 
    0x105, 0x106, 0x7, 0xc, 0x2, 0x2, 0x106, 0x10d, 0x7, 0x11, 0x2, 0x2, 
    0x107, 0x109, 0x7, 0xd, 0x2, 0x2, 0x108, 0x10a, 0x5, 0x24, 0x13, 0x2, 
    0x109, 0x108, 0x3, 0x2, 0x2, 0x2, 0x109, 0x10a, 0x3, 0x2, 0x2, 0x2, 
    0x10a, 0x10b, 0x3, 0x2, 0x2, 0x2, 0x10b, 0x10d, 0x7, 0x11, 0x2, 0x2, 
    0x10c, 0xe8, 0x3, 0x2, 0x2, 0x2, 0x10c, 0xed, 0x3, 0x2, 0x2, 0x2, 0x10c, 
    0xf0, 0x3, 0x2, 0x2, 0x2, 0x10c, 0xf1, 0x3, 0x2, 0x2, 0x2, 0x10c, 0xfa, 
    0x3, 0x2, 0x2, 0x2, 0x10c, 0x100, 0x3, 0x2, 0x2, 0x2, 0x10c, 0x101, 
    0x3, 0x2, 0x2, 0x2, 0x10c, 0x102, 0x3, 0x2, 0x2, 0x2, 0x10c, 0x103, 
    0x3, 0x2, 0x2, 0x2, 0x10c, 0x105, 0x3, 0x2, 0x2, 0x2, 0x10c, 0x107, 
    0x3, 0x2, 0x2, 0x2, 0x10d, 0x23, 0x3, 0x2, 0x2, 0x2, 0x10e, 0x10f, 0x5, 
    0x36, 0x1c, 0x2, 0x10f, 0x25, 0x3, 0x2, 0x2, 0x2, 0x110, 0x111, 0x5, 
    0x3e, 0x20, 0x2, 0x111, 0x27, 0x3, 0x2, 0x2, 0x2, 0x112, 0x119, 0x7, 
    0x28, 0x2, 0x2, 0x113, 0x114, 0x7, 0x17, 0x2, 0x2, 0x114, 0x115, 0x5, 
    0x24, 0x13, 0x2, 0x115, 0x116, 0x7, 0x18, 0x2, 0x2, 0x116, 0x118, 0x3, 
    0x2, 0x2, 0x2, 0x117, 0x113, 0x3, 0x2, 0x2, 0x2, 0x118, 0x11b, 0x3, 
    0x2, 0x2, 0x2, 0x119, 0x117, 0x3, 0x2, 0x2, 0x2, 0x119, 0x11a, 0x3, 
    0x2, 0x2, 0x2, 0x11a, 0x29, 0x3, 0x2, 0x2, 0x2, 0x11b, 0x119, 0x3, 0x2, 
    0x2, 0x2, 0x11c, 0x11d, 0x7, 0x13, 0x2, 0x2, 0x11d, 0x11e, 0x5, 0x24, 
    0x13, 0x2, 0x11e, 0x11f, 0x7, 0x14, 0x2, 0x2, 0x11f, 0x123, 0x3, 0x2, 
    0x2, 0x2, 0x120, 0x123, 0x5, 0x28, 0x15, 0x2, 0x121, 0x123, 0x5, 0x2c, 
    0x17, 0x2, 0x122, 0x11c, 0x3, 0x2, 0x2, 0x2, 0x122, 0x120, 0x3, 0x2, 
    0x2, 0x2, 0x122, 0x121, 0x3, 0x2, 0x2, 0x2, 0x123, 0x2b, 0x3, 0x2, 0x2, 
    0x2, 0x124, 0x125, 0x9, 0x3, 0x2, 0x2, 0x125, 0x2d, 0x3, 0x2, 0x2, 0x2, 
    0x126, 0x131, 0x5, 0x2a, 0x16, 0x2, 0x127, 0x128, 0x7, 0x28, 0x2, 0x2, 
    0x128, 0x12a, 0x7, 0x13, 0x2, 0x2, 0x129, 0x12b, 0x5, 0x32, 0x1a, 0x2, 
    0x12a, 0x129, 0x3, 0x2, 0x2, 0x2, 0x12a, 0x12b, 0x3, 0x2, 0x2, 0x2, 
    0x12b, 0x12c, 0x3, 0x2, 0x2, 0x2, 0x12c, 0x131, 0x7, 0x14, 0x2, 0x2, 
    0x12d, 0x12e, 0x5, 0x30, 0x19, 0x2, 0x12e, 0x12f, 0x5, 0x2e, 0x18, 0x2, 
    0x12f, 0x131, 0x3, 0x2, 0x2, 0x2, 0x130, 0x126, 0x3, 0x2, 0x2, 0x2, 
    0x130, 0x127, 0x3, 0x2, 0x2, 0x2, 0x130, 0x12d, 0x3, 0x2, 0x2, 0x2, 
    0x131, 0x2f, 0x3, 0x2, 0x2, 0x2, 0x132, 0x133, 0x9, 0x4, 0x2, 0x2, 0x133, 
    0x31, 0x3, 0x2, 0x2, 0x2, 0x134, 0x139, 0x5, 0x24, 0x13, 0x2, 0x135, 
    0x136, 0x7, 0x12, 0x2, 0x2, 0x136, 0x138, 0x5, 0x24, 0x13, 0x2, 0x137, 
    0x135, 0x3, 0x2, 0x2, 0x2, 0x138, 0x13b, 0x3, 0x2, 0x2, 0x2, 0x139, 
    0x137, 0x3, 0x2, 0x2, 0x2, 0x139, 0x13a, 0x3, 0x2, 0x2, 0x2, 0x13a, 
    0x33, 0x3, 0x2, 0x2, 0x2, 0x13b, 0x139, 0x3, 0x2, 0x2, 0x2, 0x13c, 0x13d, 
    0x8, 0x1b, 0x1, 0x2, 0x13d, 0x13e, 0x5, 0x2e, 0x18, 0x2, 0x13e, 0x144, 
    0x3, 0x2, 0x2, 0x2, 0x13f, 0x140, 0xc, 0x3, 0x2, 0x2, 0x140, 0x141, 
    0x9, 0x5, 0x2, 0x2, 0x141, 0x143, 0x5, 0x2e, 0x18, 0x2, 0x142, 0x13f, 
    0x3, 0x2, 0x2, 0x2, 0x143, 0x146, 0x3, 0x2, 0x2, 0x2, 0x144, 0x142, 
    0x3, 0x2, 0x2, 0x2, 0x144, 0x145, 0x3, 0x2, 0x2, 0x2, 0x145, 0x35, 0x3, 
    0x2, 0x2, 0x2, 0x146, 0x144, 0x3, 0x2, 0x2, 0x2, 0x147, 0x148, 0x8, 
    0x1c, 0x1, 0x2, 0x148, 0x149, 0x5, 0x34, 0x1b, 0x2, 0x149, 0x14f, 0x3, 
    0x2, 0x2, 0x2, 0x14a, 0x14b, 0xc, 0x3, 0x2, 0x2, 0x14b, 0x14c, 0x9, 
    0x6, 0x2, 0x2, 0x14c, 0x14e, 0x5, 0x34, 0x1b, 0x2, 0x14d, 0x14a, 0x3, 
    0x2, 0x2, 0x2, 0x14e, 0x151, 0x3, 0x2, 0x2, 0x2, 0x14f, 0x14d, 0x3, 
    0x2, 0x2, 0x2, 0x14f, 0x150, 0x3, 0x2, 0x2, 0x2, 0x150, 0x37, 0x3, 0x2, 
    0x2, 0x2, 0x151, 0x14f, 0x3, 0x2, 0x2, 0x2, 0x152, 0x153, 0x8, 0x1d, 
    0x1, 0x2, 0x153, 0x154, 0x5, 0x36, 0x1c, 0x2, 0x154, 0x15a, 0x3, 0x2, 
    0x2, 0x2, 0x155, 0x156, 0xc, 0x3, 0x2, 0x2, 0x156, 0x157, 0x9, 0x7, 
    0x2, 0x2, 0x157, 0x159, 0x5, 0x36, 0x1c, 0x2, 0x158, 0x155, 0x3, 0x2, 
    0x2, 0x2, 0x159, 0x15c, 0x3, 0x2, 0x2, 0x2, 0x15a, 0x158, 0x3, 0x2, 
    0x2, 0x2, 0x15a, 0x15b, 0x3, 0x2, 0x2, 0x2, 0x15b, 0x39, 0x3, 0x2, 0x2, 
    0x2, 0x15c, 0x15a, 0x3, 0x2, 0x2, 0x2, 0x15d, 0x15e, 0x8, 0x1e, 0x1, 
    0x2, 0x15e, 0x15f, 0x5, 0x38, 0x1d, 0x2, 0x15f, 0x165, 0x3, 0x2, 0x2, 
    0x2, 0x160, 0x161, 0xc, 0x3, 0x2, 0x2, 0x161, 0x162, 0x9, 0x8, 0x2, 
    0x2, 0x162, 0x164, 0x5, 0x38, 0x1d, 0x2, 0x163, 0x160, 0x3, 0x2, 0x2, 
    0x2, 0x164, 0x167, 0x3, 0x2, 0x2, 0x2, 0x165, 0x163, 0x3, 0x2, 0x2, 
    0x2, 0x165, 0x166, 0x3, 0x2, 0x2, 0x2, 0x166, 0x3b, 0x3, 0x2, 0x2, 0x2, 
    0x167, 0x165, 0x3, 0x2, 0x2, 0x2, 0x168, 0x169, 0x8, 0x1f, 0x1, 0x2, 
    0x169, 0x16a, 0x5, 0x3a, 0x1e, 0x2, 0x16a, 0x170, 0x3, 0x2, 0x2, 0x2, 
    0x16b, 0x16c, 0xc, 0x3, 0x2, 0x2, 0x16c, 0x16d, 0x7, 0x25, 0x2, 0x2, 
    0x16d, 0x16f, 0x5, 0x3a, 0x1e, 0x2, 0x16e, 0x16b, 0x3, 0x2, 0x2, 0x2, 
    0x16f, 0x172, 0x3, 0x2, 0x2, 0x2, 0x170, 0x16e, 0x3, 0x2, 0x2, 0x2, 
    0x170, 0x171, 0x3, 0x2, 0x2, 0x2, 0x171, 0x3d, 0x3, 0x2, 0x2, 0x2, 0x172, 
    0x170, 0x3, 0x2, 0x2, 0x2, 0x173, 0x174, 0x8, 0x20, 0x1, 0x2, 0x174, 
    0x175, 0x5, 0x3c, 0x1f, 0x2, 0x175, 0x17b, 0x3, 0x2, 0x2, 0x2, 0x176, 
    0x177, 0xc, 0x3, 0x2, 0x2, 0x177, 0x178, 0x7, 0x26, 0x2, 0x2, 0x178, 
    0x17a, 0x5, 0x3c, 0x1f, 0x2, 0x179, 0x176, 0x3, 0x2, 0x2, 0x2, 0x17a, 
    0x17d, 0x3, 0x2, 0x2, 0x2, 0x17b, 0x179, 0x3, 0x2, 0x2, 0x2, 0x17b, 
    0x17c, 0x3, 0x2, 0x2, 0x2, 0x17c, 0x3f, 0x3, 0x2, 0x2, 0x2, 0x17d, 0x17b, 
    0x3, 0x2, 0x2, 0x2, 0x17e, 0x17f, 0x5, 0x36, 0x1c, 0x2, 0x17f, 0x41, 
    0x3, 0x2, 0x2, 0x2, 0x180, 0x181, 0x7, 0xa, 0x2, 0x2, 0x181, 0x182, 
    0x7, 0x13, 0x2, 0x2, 0x182, 0x183, 0x5, 0x44, 0x23, 0x2, 0x183, 0x184, 
    0x7, 0x11, 0x2, 0x2, 0x184, 0x185, 0x5, 0x26, 0x14, 0x2, 0x185, 0x186, 
    0x7, 0x11, 0x2, 0x2, 0x186, 0x187, 0x5, 0x46, 0x24, 0x2, 0x187, 0x188, 
    0x7, 0x14, 0x2, 0x2, 0x188, 0x189, 0x5, 0x22, 0x12, 0x2, 0x189, 0x43, 
    0x3, 0x2, 0x2, 0x2, 0x18a, 0x191, 0x5, 0x10, 0x9, 0x2, 0x18b, 0x18c, 
    0x5, 0x28, 0x15, 0x2, 0x18c, 0x18d, 0x7, 0x19, 0x2, 0x2, 0x18d, 0x18e, 
    0x5, 0x24, 0x13, 0x2, 0x18e, 0x191, 0x3, 0x2, 0x2, 0x2, 0x18f, 0x191, 
    0x3, 0x2, 0x2, 0x2, 0x190, 0x18a, 0x3, 0x2, 0x2, 0x2, 0x190, 0x18b, 
    0x3, 0x2, 0x2, 0x2, 0x190, 0x18f, 0x3, 0x2, 0x2, 0x2, 0x191, 0x45, 0x3, 
    0x2, 0x2, 0x2, 0x192, 0x193, 0x5, 0x28, 0x15, 0x2, 0x193, 0x194, 0x7, 
    0x19, 0x2, 0x2, 0x194, 0x195, 0x5, 0x24, 0x13, 0x2, 0x195, 0x198, 0x3, 
    0x2, 0x2, 0x2, 0x196, 0x198, 0x3, 0x2, 0x2, 0x2, 0x197, 0x192, 0x3, 
    0x2, 0x2, 0x2, 0x197, 0x196, 0x3, 0x2, 0x2, 0x2, 0x198, 0x47, 0x3, 0x2, 
    0x2, 0x2, 0x199, 0x19a, 0x7, 0xe, 0x2, 0x2, 0x19a, 0x19b, 0x7, 0xf, 
    0x2, 0x2, 0x19b, 0x19c, 0x7, 0xa, 0x2, 0x2, 0x19c, 0x19d, 0x7, 0xa, 
    0x2, 0x2, 0x19d, 0x19e, 0x7, 0x13, 0x2, 0x2, 0x19e, 0x19f, 0x5, 0x44, 
    0x23, 0x2, 0x19f, 0x1a0, 0x7, 0x11, 0x2, 0x2, 0x1a0, 0x1a1, 0x5, 0x26, 
    0x14, 0x2, 0x1a1, 0x1a2, 0x7, 0x11, 0x2, 0x2, 0x1a2, 0x1a3, 0x5, 0x46, 
    0x24, 0x2, 0x1a3, 0x1a4, 0x7, 0x14, 0x2, 0x2, 0x1a4, 0x1a5, 0x5, 0x22, 
    0x12, 0x2, 0x1a5, 0x49, 0x3, 0x2, 0x2, 0x2, 0x1a6, 0x1a7, 0x7, 0x10, 
    0x2, 0x2, 0x1a7, 0x1a8, 0x7, 0x11, 0x2, 0x2, 0x1a8, 0x4b, 0x3, 0x2, 
    0x2, 0x2, 0x28, 0x4f, 0x55, 0x59, 0x62, 0x70, 0x7d, 0x80, 0x83, 0x8b, 
    0x97, 0xa1, 0xa6, 0xaf, 0xb2, 0xb5, 0xbb, 0xc2, 0xc9, 0xd6, 0xd9, 0xdf, 
    0xe6, 0xf8, 0x109, 0x10c, 0x119, 0x122, 0x12a, 0x130, 0x139, 0x144, 
    0x14f, 0x15a, 0x165, 0x170, 0x17b, 0x190, 0x197, 
  };

  atn::ATNDeserializer deserializer;
  _atn = deserializer.deserialize(_serializedATN);

  size_t count = _atn.getNumberOfDecisions();
  _decisionToDFA.reserve(count);
  for (size_t i = 0; i < count; i++) { 
    _decisionToDFA.emplace_back(_atn.getDecisionState(i), i);
  }
}

SysYExtendedParser::Initializer SysYExtendedParser::_init;
