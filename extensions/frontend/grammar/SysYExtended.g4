grammar SysYExtended;

import SysYExtendedLex;

program
    : compUnit+ EOF;

compUnit
    : decl | funcDef;

// Declaration
decl
    : constDecl | varDecl;

constDecl
    : CONST bType constDef ( COMMA constDef )* SEMICOLON;

bType: INT | FLOAT;

constDef
    : IDENT ( LBRACKET constExp RBRACKET )* ASSIGN constInitVal;

constInitVal
    : constExp
    | LBRACE ( constInitVal ( COMMA constInitVal )* )? RBRACE;

// Variable Declaration
varDecl
    : bType varDef ( COMMA varDef )* SEMICOLON;

varDef
    : IDENT ( LBRACKET constExp RBRACKET )*
    | IDENT ( LBRACKET constExp RBRACKET )* ASSIGN initVal;

initVal
    : exp
    | LBRACE ( initVal ( COMMA initVal )* )? RBRACE;

// Function Declaration
funcDef
    : funcType IDENT LPAREN ( funcFormalParams )? RPAREN block;

funcType
    : VOID | bType;

funcFormalParams
    : funcFormalParam ( COMMA funcFormalParam )*;

funcFormalParam
    : bType IDENT ( LBRACKET RBRACKET (LBRACKET exp RBRACKET)* )?;

block
    : LBRACE blockItem* RBRACE;

blockItem
    : decl | stmt;

stmt
    : lVal ASSIGN exp SEMICOLON
    | exp SEMICOLON
    | block
    | IF LPAREN cond RPAREN stmt ( ELSE stmt )?
    | WHILE LPAREN cond RPAREN stmt
    | forStmt
    | parallelForStmt
    | barrierStmt
    | BREAK SEMICOLON
    | CONTINUE SEMICOLON
    | RETURN ( exp )? SEMICOLON;

exp : addExp;

cond: lOrExp;

lVal: IDENT ( LBRACKET exp RBRACKET )*;

primaryExp
    : LPAREN exp RPAREN | lVal | number ;

number
    : INTCONST | FLOATCONST;

unaryExp
    : primaryExp
    | IDENT LPAREN funcRealParams? RPAREN
    | unaryOP unaryExp;

unaryOP
    : ADD | SUB | NOT;

funcRealParams
    : exp ( COMMA exp )*;

mulExp
    : unaryExp
    | mulExp ( MUL | DIV | MOD ) unaryExp;

addExp
    : mulExp
    | addExp ( ADD | SUB ) mulExp;

relExp
    : addExp
    | relExp ( LT | GT | LE | GE ) addExp ;

eqExp
    : relExp
    | eqExp ( EQ | NE ) relExp;

lAndExp
    : eqExp
    | lAndExp AND eqExp;

lOrExp
    : lAndExp
    | lOrExp OR lAndExp;

constExp
    : addExp;

// For loop statement
forStmt
    : FOR LPAREN forInit SEMICOLON cond SEMICOLON forUpdate RPAREN stmt;

forInit
    : varDecl
    | lVal ASSIGN exp
    | /* empty */;

forUpdate
    : lVal ASSIGN exp
    | /* empty */;

// Parallel for loop statement
parallelForStmt
    : PRAGMA PARALLEL FOR FOR LPAREN forInit SEMICOLON cond SEMICOLON forUpdate RPAREN stmt;

// Barrier statement
barrierStmt
    : BARRIER SEMICOLON;










