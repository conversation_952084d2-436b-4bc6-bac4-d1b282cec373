
// Generated from grammar/SysYExtended.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"
#include "SysYExtendedListener.h"


/**
 * This class provides an empty implementation of SysYExtendedListener,
 * which can be extended to create a listener which only needs to handle a subset
 * of the available methods.
 */
class  SysYExtendedBaseListener : public SysYExtendedListener {
public:

  virtual void enterProgram(SysYExtendedParser::ProgramContext * /*ctx*/) override { }
  virtual void exitProgram(SysYExtendedParser::ProgramContext * /*ctx*/) override { }

  virtual void enterCompUnit(SysYExtendedParser::CompUnitContext * /*ctx*/) override { }
  virtual void exitCompUnit(SysYExtendedParser::CompUnitContext * /*ctx*/) override { }

  virtual void enterDecl(SysYExtendedParser::DeclContext * /*ctx*/) override { }
  virtual void exitDecl(SysYExtendedParser::DeclContext * /*ctx*/) override { }

  virtual void enterConstDecl(SysYExtendedParser::ConstDeclContext * /*ctx*/) override { }
  virtual void exitConstDecl(SysYExtendedParser::ConstDeclContext * /*ctx*/) override { }

  virtual void enterBType(SysYExtendedParser::BTypeContext * /*ctx*/) override { }
  virtual void exitBType(SysYExtendedParser::BTypeContext * /*ctx*/) override { }

  virtual void enterConstDef(SysYExtendedParser::ConstDefContext * /*ctx*/) override { }
  virtual void exitConstDef(SysYExtendedParser::ConstDefContext * /*ctx*/) override { }

  virtual void enterConstInitVal(SysYExtendedParser::ConstInitValContext * /*ctx*/) override { }
  virtual void exitConstInitVal(SysYExtendedParser::ConstInitValContext * /*ctx*/) override { }

  virtual void enterVarDecl(SysYExtendedParser::VarDeclContext * /*ctx*/) override { }
  virtual void exitVarDecl(SysYExtendedParser::VarDeclContext * /*ctx*/) override { }

  virtual void enterVarDef(SysYExtendedParser::VarDefContext * /*ctx*/) override { }
  virtual void exitVarDef(SysYExtendedParser::VarDefContext * /*ctx*/) override { }

  virtual void enterInitVal(SysYExtendedParser::InitValContext * /*ctx*/) override { }
  virtual void exitInitVal(SysYExtendedParser::InitValContext * /*ctx*/) override { }

  virtual void enterFuncDef(SysYExtendedParser::FuncDefContext * /*ctx*/) override { }
  virtual void exitFuncDef(SysYExtendedParser::FuncDefContext * /*ctx*/) override { }

  virtual void enterFuncType(SysYExtendedParser::FuncTypeContext * /*ctx*/) override { }
  virtual void exitFuncType(SysYExtendedParser::FuncTypeContext * /*ctx*/) override { }

  virtual void enterFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext * /*ctx*/) override { }
  virtual void exitFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext * /*ctx*/) override { }

  virtual void enterFuncFormalParam(SysYExtendedParser::FuncFormalParamContext * /*ctx*/) override { }
  virtual void exitFuncFormalParam(SysYExtendedParser::FuncFormalParamContext * /*ctx*/) override { }

  virtual void enterBlock(SysYExtendedParser::BlockContext * /*ctx*/) override { }
  virtual void exitBlock(SysYExtendedParser::BlockContext * /*ctx*/) override { }

  virtual void enterBlockItem(SysYExtendedParser::BlockItemContext * /*ctx*/) override { }
  virtual void exitBlockItem(SysYExtendedParser::BlockItemContext * /*ctx*/) override { }

  virtual void enterStmt(SysYExtendedParser::StmtContext * /*ctx*/) override { }
  virtual void exitStmt(SysYExtendedParser::StmtContext * /*ctx*/) override { }

  virtual void enterExp(SysYExtendedParser::ExpContext * /*ctx*/) override { }
  virtual void exitExp(SysYExtendedParser::ExpContext * /*ctx*/) override { }

  virtual void enterCond(SysYExtendedParser::CondContext * /*ctx*/) override { }
  virtual void exitCond(SysYExtendedParser::CondContext * /*ctx*/) override { }

  virtual void enterLVal(SysYExtendedParser::LValContext * /*ctx*/) override { }
  virtual void exitLVal(SysYExtendedParser::LValContext * /*ctx*/) override { }

  virtual void enterPrimaryExp(SysYExtendedParser::PrimaryExpContext * /*ctx*/) override { }
  virtual void exitPrimaryExp(SysYExtendedParser::PrimaryExpContext * /*ctx*/) override { }

  virtual void enterNumber(SysYExtendedParser::NumberContext * /*ctx*/) override { }
  virtual void exitNumber(SysYExtendedParser::NumberContext * /*ctx*/) override { }

  virtual void enterUnaryExp(SysYExtendedParser::UnaryExpContext * /*ctx*/) override { }
  virtual void exitUnaryExp(SysYExtendedParser::UnaryExpContext * /*ctx*/) override { }

  virtual void enterUnaryOP(SysYExtendedParser::UnaryOPContext * /*ctx*/) override { }
  virtual void exitUnaryOP(SysYExtendedParser::UnaryOPContext * /*ctx*/) override { }

  virtual void enterFuncRealParams(SysYExtendedParser::FuncRealParamsContext * /*ctx*/) override { }
  virtual void exitFuncRealParams(SysYExtendedParser::FuncRealParamsContext * /*ctx*/) override { }

  virtual void enterMulExp(SysYExtendedParser::MulExpContext * /*ctx*/) override { }
  virtual void exitMulExp(SysYExtendedParser::MulExpContext * /*ctx*/) override { }

  virtual void enterAddExp(SysYExtendedParser::AddExpContext * /*ctx*/) override { }
  virtual void exitAddExp(SysYExtendedParser::AddExpContext * /*ctx*/) override { }

  virtual void enterRelExp(SysYExtendedParser::RelExpContext * /*ctx*/) override { }
  virtual void exitRelExp(SysYExtendedParser::RelExpContext * /*ctx*/) override { }

  virtual void enterEqExp(SysYExtendedParser::EqExpContext * /*ctx*/) override { }
  virtual void exitEqExp(SysYExtendedParser::EqExpContext * /*ctx*/) override { }

  virtual void enterLAndExp(SysYExtendedParser::LAndExpContext * /*ctx*/) override { }
  virtual void exitLAndExp(SysYExtendedParser::LAndExpContext * /*ctx*/) override { }

  virtual void enterLOrExp(SysYExtendedParser::LOrExpContext * /*ctx*/) override { }
  virtual void exitLOrExp(SysYExtendedParser::LOrExpContext * /*ctx*/) override { }

  virtual void enterConstExp(SysYExtendedParser::ConstExpContext * /*ctx*/) override { }
  virtual void exitConstExp(SysYExtendedParser::ConstExpContext * /*ctx*/) override { }

  virtual void enterForStmt(SysYExtendedParser::ForStmtContext * /*ctx*/) override { }
  virtual void exitForStmt(SysYExtendedParser::ForStmtContext * /*ctx*/) override { }

  virtual void enterForInit(SysYExtendedParser::ForInitContext * /*ctx*/) override { }
  virtual void exitForInit(SysYExtendedParser::ForInitContext * /*ctx*/) override { }

  virtual void enterForUpdate(SysYExtendedParser::ForUpdateContext * /*ctx*/) override { }
  virtual void exitForUpdate(SysYExtendedParser::ForUpdateContext * /*ctx*/) override { }

  virtual void enterParallelForStmt(SysYExtendedParser::ParallelForStmtContext * /*ctx*/) override { }
  virtual void exitParallelForStmt(SysYExtendedParser::ParallelForStmtContext * /*ctx*/) override { }

  virtual void enterBarrierStmt(SysYExtendedParser::BarrierStmtContext * /*ctx*/) override { }
  virtual void exitBarrierStmt(SysYExtendedParser::BarrierStmtContext * /*ctx*/) override { }


  virtual void enterEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void exitEveryRule(antlr4::ParserRuleContext * /*ctx*/) override { }
  virtual void visitTerminal(antlr4::tree::TerminalNode * /*node*/) override { }
  virtual void visitErrorNode(antlr4::tree::ErrorNode * /*node*/) override { }

};

