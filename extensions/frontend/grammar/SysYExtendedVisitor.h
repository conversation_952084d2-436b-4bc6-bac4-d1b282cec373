
// Generated from grammar/SysYExtended.g4 by ANTLR 4.7.2

#pragma once


#include "antlr4-runtime.h"
#include "SysYExtendedParser.h"



/**
 * This class defines an abstract visitor for a parse tree
 * produced by SysYExtendedParser.
 */
class  SysYExtendedVisitor : public antlr4::tree::AbstractParseTreeVisitor {
public:

  /**
   * Visit parse trees produced by SysYExtendedParser.
   */
    virtual antlrcpp::Any visitProgram(SysYExtendedParser::ProgramContext *context) = 0;

    virtual antlrcpp::Any visitCompUnit(SysYExtendedParser::CompUnitContext *context) = 0;

    virtual antlrcpp::Any visitDecl(SysYExtendedParser::DeclContext *context) = 0;

    virtual antlrcpp::Any visitConstDecl(SysYExtendedParser::ConstDeclContext *context) = 0;

    virtual antlrcpp::Any visitBType(SysYExtendedParser::BTypeContext *context) = 0;

    virtual antlrcpp::Any visitConstDef(SysYExtendedParser::ConstDefContext *context) = 0;

    virtual antlrcpp::Any visitConstInitVal(SysYExtendedParser::ConstInitValContext *context) = 0;

    virtual antlrcpp::Any visitVarDecl(SysYExtendedParser::VarDeclContext *context) = 0;

    virtual antlrcpp::Any visitVarDef(SysYExtendedParser::VarDefContext *context) = 0;

    virtual antlrcpp::Any visitInitVal(SysYExtendedParser::InitValContext *context) = 0;

    virtual antlrcpp::Any visitFuncDef(SysYExtendedParser::FuncDefContext *context) = 0;

    virtual antlrcpp::Any visitFuncType(SysYExtendedParser::FuncTypeContext *context) = 0;

    virtual antlrcpp::Any visitFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext *context) = 0;

    virtual antlrcpp::Any visitFuncFormalParam(SysYExtendedParser::FuncFormalParamContext *context) = 0;

    virtual antlrcpp::Any visitBlock(SysYExtendedParser::BlockContext *context) = 0;

    virtual antlrcpp::Any visitBlockItem(SysYExtendedParser::BlockItemContext *context) = 0;

    virtual antlrcpp::Any visitStmt(SysYExtendedParser::StmtContext *context) = 0;

    virtual antlrcpp::Any visitExp(SysYExtendedParser::ExpContext *context) = 0;

    virtual antlrcpp::Any visitCond(SysYExtendedParser::CondContext *context) = 0;

    virtual antlrcpp::Any visitLVal(SysYExtendedParser::LValContext *context) = 0;

    virtual antlrcpp::Any visitPrimaryExp(SysYExtendedParser::PrimaryExpContext *context) = 0;

    virtual antlrcpp::Any visitNumber(SysYExtendedParser::NumberContext *context) = 0;

    virtual antlrcpp::Any visitUnaryExp(SysYExtendedParser::UnaryExpContext *context) = 0;

    virtual antlrcpp::Any visitUnaryOP(SysYExtendedParser::UnaryOPContext *context) = 0;

    virtual antlrcpp::Any visitFuncRealParams(SysYExtendedParser::FuncRealParamsContext *context) = 0;

    virtual antlrcpp::Any visitMulExp(SysYExtendedParser::MulExpContext *context) = 0;

    virtual antlrcpp::Any visitAddExp(SysYExtendedParser::AddExpContext *context) = 0;

    virtual antlrcpp::Any visitRelExp(SysYExtendedParser::RelExpContext *context) = 0;

    virtual antlrcpp::Any visitEqExp(SysYExtendedParser::EqExpContext *context) = 0;

    virtual antlrcpp::Any visitLAndExp(SysYExtendedParser::LAndExpContext *context) = 0;

    virtual antlrcpp::Any visitLOrExp(SysYExtendedParser::LOrExpContext *context) = 0;

    virtual antlrcpp::Any visitConstExp(SysYExtendedParser::ConstExpContext *context) = 0;

    virtual antlrcpp::Any visitForStmt(SysYExtendedParser::ForStmtContext *context) = 0;

    virtual antlrcpp::Any visitForInit(SysYExtendedParser::ForInitContext *context) = 0;

    virtual antlrcpp::Any visitForUpdate(SysYExtendedParser::ForUpdateContext *context) = 0;

    virtual antlrcpp::Any visitParallelForStmt(SysYExtendedParser::ParallelForStmtContext *context) = 0;

    virtual antlrcpp::Any visitBarrierStmt(SysYExtendedParser::BarrierStmtContext *context) = 0;


};

