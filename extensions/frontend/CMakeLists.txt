cmake_minimum_required(VERSION 3.16)

# Generate ANTLR4 files for extended grammar
find_program(ANTLR4_JAR antlr4 PATHS /usr/share/java)
if(NOT ANTLR4_JAR)
    find_program(ANTLR4_JAR antlr-4.13.1-complete.jar PATHS /usr/share/java)
endif()

if(NOT ANTLR4_JAR)
    message(FATAL_ERROR "ANTLR4 jar not found. Please install antlr4 or provide the path.")
endif()

# Create generated directory
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/generated)

# Generate extended grammar files
add_custom_command(
    OUTPUT
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedLexer.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedLexer.h
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedParser.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedParser.h
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedBaseVisitor.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedBaseVisitor.h
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedVisitor.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/generated/SysYExtendedVisitor.h
    COMMAND java -jar ${ANTLR4_JAR} -Dlanguage=Cpp -visitor -o generated grammar/SysYExtended.g4
    DEPENDS
        ${CMAKE_CURRENT_SOURCE_DIR}/grammar/SysYExtended.g4
        ${CMAKE_CURRENT_SOURCE_DIR}/grammar/SysYExtendedLex.g4
    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
    COMMENT "Generating extended ANTLR4 files"
)

# Create frontend_extended library
add_library(frontend_extended
    generated/SysYExtendedLexer.cpp
    generated/SysYExtendedParser.cpp
    generated/SysYExtendedBaseVisitor.cpp
    generated/SysYExtendedVisitor.cpp
)

target_include_directories(frontend_extended PUBLIC
    ${ANTLR4_INCLUDE_DIR}
    generated
)

target_link_libraries(frontend_extended
    ${ANTLR4_LIBRARIES}
)
