cmake_minimum_required(VERSION 3.16)

# Create backend_extended library
add_library(backend_extended
    ASTVisitorExtended.cpp
    TypeExtended.cpp
)

target_include_directories(backend_extended PUBLIC
    ${ANTLR4_INCLUDE_DIR}
    ${LLVM_INCLUDE_DIRS}
    ../frontend/generated
    .
    ../../backend
    ../../backend/ir_generation
    ../../backend/optimization
    ../../backend/codegen
    ../runtime
)

target_link_libraries(backend_extended
    ${ANTLR4_LIBRARIES}
    ${llvm_libs}
    pthread
)
