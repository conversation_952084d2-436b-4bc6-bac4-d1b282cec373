#pragma once
#include "antlr4-runtime.h"
#include "SysYExtendedBaseVisitor.h"
#include <iostream>
// #include <llvm-14/llvm/ADT/STLFunctionalExtras.h>
// #include <llvm-14/llvm/IR/DerivedTypes.h>
#include <memory>

// LLVM includes
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/IRBuilder.h"
#include "llvm/IR/Value.h"
#include "llvm/IR/Function.h"
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Verifier.h"
#include "llvm/Support/raw_ostream.h"
// Pass related includes
#include "llvm/IR/PassManager.h"
#include "llvm/Passes/PassBuilder.h"
#include "llvm/Passes/StandardInstrumentations.h"
#include "llvm/Transforms/InstCombine/InstCombine.h"
#include "llvm/Transforms/Scalar.h"
#include "llvm/Transforms/Scalar/GVN.h"
#include "llvm/Transforms/Scalar/Reassociate.h"
#include "llvm/Transforms/Scalar/SimplifyCFG.h"
#include "llvm/Transforms/Utils/Mem2Reg.h"
#include "llvm/IR/LegacyPassManager.h"
#include "llvm/Transforms/Scalar.h"


class ASTVisitorExtended : public SysYExtendedBaseVisitor {
private:
    int indent = 0;

    // LLVM components
    std::unique_ptr<llvm::LLVMContext> llvm_context;
    std::unique_ptr<llvm::Module> llvm_module;
    std::unique_ptr<llvm::IRBuilder<>> llvm_builder;
    
    // Symbol table for variables
    std::map<std::string, std::vector<llvm::Value*>> named_values;


    std::map<std::string, llvm::Function*> function_table;
    // llvm::BasicBlock* current_block = nullptr;
    llvm::Function* current_function = nullptr;

    // LLVM pass and analysis managers:
    static std::unique_ptr<llvm::FunctionPassManager> function_pass_manager;
    static std::unique_ptr<llvm::LoopAnalysisManager> loop_analysis_manager;
    static std::unique_ptr<llvm::FunctionAnalysisManager> function_analysis_manager;
    static std::unique_ptr<llvm::CGSCCAnalysisManager> cgscc_analysis_manager;
    static std::unique_ptr<llvm::ModuleAnalysisManager> module_analysis_manager;
    static std::unique_ptr<llvm::PassInstrumentationCallbacks> pass_instrumentation_callbacks;
    static std::unique_ptr<llvm::StandardInstrumentations> standard_instrumentations;


    llvm::Type* current_type = nullptr;
    llvm::ArrayType* current_array_type = nullptr;

    void printIndent() {
        for (int i = 0; i < indent; i++) std::cout << "  ";
    }

    // Helper function to create zero-initialized array constants
    llvm::Constant* createZeroInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions);

    // Scope management helpers
    void pushScope();
    void popScope();

    // Loop context management helpers
    void pushLoop(llvm::BasicBlock* break_target, llvm::BasicBlock* continue_target, const std::string& loop_name);
    void popLoop();
    llvm::BasicBlock* getCurrentBreakTarget();
    llvm::BasicBlock* getCurrentContinueTarget();

private:
    // Stack of symbol tables for nested scopes
    std::vector<std::map<std::string, std::vector<llvm::Value*>>> scope_stack;

    // Loop context for break/continue statements
    struct LoopContext {
        llvm::BasicBlock* break_target;    // Where to jump on break
        llvm::BasicBlock* continue_target; // Where to jump on continue
        std::string loop_name;             // For debugging
    };
    std::vector<LoopContext> loop_stack;

    // Initialize SysY runtime library functions (external declarations only)
    void initializeRuntimeLibrary();

public:
    ASTVisitorExtended();

    // Run optimization passes including mem2reg
    void optimize_ir();
    void generate_llvm_ir();
    void print_llvm_ir();


    antlrcpp::Any visitCompUnit(SysYExtendedParser::CompUnitContext *ctx) override;
    antlrcpp::Any visitProgram(SysYExtendedParser::ProgramContext *ctx) override;
    antlrcpp::Any visitDecl(SysYExtendedParser::DeclContext *ctx) override;
    antlrcpp::Any visitConstDecl(SysYExtendedParser::ConstDeclContext *ctx) override;
    antlrcpp::Any visitBType(SysYExtendedParser::BTypeContext *ctx) override;
    antlrcpp::Any visitConstDef(SysYExtendedParser::ConstDefContext *ctx) override;
    antlrcpp::Any visitConstInitVal(SysYExtendedParser::ConstInitValContext *ctx) override;
    antlrcpp::Any visitVarDecl(SysYExtendedParser::VarDeclContext *ctx) override;
    antlrcpp::Any visitVarDef(SysYExtendedParser::VarDefContext *ctx) override;
    antlrcpp::Any visitInitVal(SysYExtendedParser::InitValContext *ctx) override;
    antlrcpp::Any visitFuncDef(SysYExtendedParser::FuncDefContext *ctx) override;
    antlrcpp::Any visitFuncType(SysYExtendedParser::FuncTypeContext *ctx) override;
    antlrcpp::Any visitFuncFormalParams(SysYExtendedParser::FuncFormalParamsContext *ctx) override;
    antlrcpp::Any visitFuncFormalParam(SysYExtendedParser::FuncFormalParamContext *ctx) override;
    antlrcpp::Any visitFuncRealParams(SysYExtendedParser::FuncRealParamsContext *ctx) override;
    antlrcpp::Any visitBlock(SysYExtendedParser::BlockContext *ctx) override;
    antlrcpp::Any visitBlockItem(SysYExtendedParser::BlockItemContext *ctx) override;
    antlrcpp::Any visitStmt(SysYExtendedParser::StmtContext *ctx) override;
    antlrcpp::Any visitExp(SysYExtendedParser::ExpContext *ctx) override;
    antlrcpp::Any visitCond(SysYExtendedParser::CondContext *ctx) override;
    antlrcpp::Any visitLVal(SysYExtendedParser::LValContext *ctx) override;
    antlrcpp::Any visitPrimaryExp(SysYExtendedParser::PrimaryExpContext *ctx) override;
    antlrcpp::Any visitNumber(SysYExtendedParser::NumberContext *ctx) override;
    antlrcpp::Any visitUnaryExp(SysYExtendedParser::UnaryExpContext *ctx) override;
    antlrcpp::Any visitUnaryOP(SysYExtendedParser::UnaryOPContext *ctx) override;
    antlrcpp::Any visitMulExp(SysYExtendedParser::MulExpContext *ctx) override;
    antlrcpp::Any visitAddExp(SysYExtendedParser::AddExpContext *ctx) override;
    antlrcpp::Any visitRelExp(SysYExtendedParser::RelExpContext *ctx) override;
    antlrcpp::Any visitEqExp(SysYExtendedParser::EqExpContext *ctx) override;
    antlrcpp::Any visitLAndExp(SysYExtendedParser::LAndExpContext *ctx) override;
    antlrcpp::Any visitLOrExp(SysYExtendedParser::LOrExpContext *ctx) override;
    antlrcpp::Any visitConstExp(SysYExtendedParser::ConstExpContext *ctx) override;

    // Extended methods for new constructs
    antlrcpp::Any visitForStmt(SysYExtendedParser::ForStmtContext *ctx) override;
    antlrcpp::Any visitForInit(SysYExtendedParser::ForInitContext *ctx) override;
    antlrcpp::Any visitForUpdate(SysYExtendedParser::ForUpdateContext *ctx) override;
    antlrcpp::Any visitParallelForStmt(SysYExtendedParser::ParallelForStmtContext *ctx) override;
    antlrcpp::Any visitBarrierStmt(SysYExtendedParser::BarrierStmtContext *ctx) override;

};