#pragma once

#include <memory>
#include <string>
#include "llvm/IR/LLVMContext.h"
#include "llvm/IR/Module.h"
#include "llvm/IR/IRBuilder.h"

// Forward declarations
#include "SysYExtendedBaseVisitor.h"
#include "../../backend/ir_generation/IRGenerator.hpp"
#include "../../backend/optimization/Optimizer.hpp"
#include "../../backend/codegen/ARMCodeGenerator.hpp"

/**
 * BackendExtended - Extended backend coordinator class for SysY Extended language
 * 
 * This class owns the LLVM components (context, module, builder) and coordinates
 * the three backend phases:
 * 1. IR Generation (AST -> LLVM IR)
 * 2. Optimization (LLVM optimization passes)
 * 3. Code Generation (LLVM IR -> object files using LLVM backend)
 * 
 * This design follows proper separation of concerns where each component
 * has a single responsibility and the Backend orchestrates the pipeline.
 */
class BackendExtended {
private:
    // LLVM components (owned by Backend)
    std::unique_ptr<llvm::LLVMContext> llvm_context;
    std::unique_ptr<llvm::Module> llvm_module;
    std::unique_ptr<llvm::IRBuilder<>> llvm_builder;
    
    // Backend components (owned by Backend)
    std::unique_ptr<IRGenerator> ir_generator;
    std::unique_ptr<Optimizer> optimizer;
    std::unique_ptr<ARMCodeGenerator> code_generator;
    
    // Configuration
    std::string module_name;
    Optimizer::OptimizationLevel opt_level;
    bool arm_target;

public:
    // Constructor
    BackendExtended(const std::string& module_name = "SysYExtended", 
            Optimizer::OptimizationLevel opt_level = Optimizer::O1,
            bool arm_target = true);
    
    ~BackendExtended() = default;

    // Main compilation pipeline methods
    void generateIR(antlr4::tree::ParseTree* ast);
    void optimizeIR();
    void generateAssembly(const std::string& output_file);
    void generateObjectFile(const std::string& output_file);
    void generateExecutable(const std::string& output_file);
    
    // Full compilation pipeline
    void compile(antlr4::tree::ParseTree* ast, const std::string& output_file, 
                 const std::string& format = "executable"); // "assembly", "object", "executable"
    
    // Utility methods
    void printLLVMIR();
    void setOptimizationLevel(Optimizer::OptimizationLevel level);
    void setARMTarget(bool enable) { arm_target = enable; }
    
    // Access to LLVM components (for advanced usage)
    llvm::Module* getModule() const { return llvm_module.get(); }
    llvm::LLVMContext* getContext() const { return llvm_context.get(); }
    llvm::IRBuilder<>* getBuilder() const { return llvm_builder.get(); }
    
    // Access to backend components
    IRGenerator* getIRGenerator() const { return ir_generator.get(); }
    Optimizer* getOptimizer() const { return optimizer.get(); }
    ARMCodeGenerator* getCodeGenerator() const { return code_generator.get(); }
};
