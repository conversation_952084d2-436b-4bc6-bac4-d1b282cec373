# Back-end modular libraries

# Main Backend
add_library(backend
    Backend.cpp
)

target_include_directories(backend PUBLIC
    .
    ir_generation
    optimization
    codegen
    ${LLVM_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/frontend/generated
)

target_link_libraries(backend
    ir_generation
    optimization
    arm_codegen
    ${llvm_libs}
    frontend
)

target_compile_definitions(backend PRIVATE ${LLVM_DEFINITIONS})

# IR Generation module
add_library(ir_generation
    ir_generation/IRGenerator.cpp
    ir_generation/Type.cpp
)

target_include_directories(ir_generation PUBLIC
    ir_generation
    ${LLVM_INCLUDE_DIRS}
    ${CMAKE_SOURCE_DIR}/frontend/generated
)

target_link_libraries(ir_generation
    ${llvm_libs}
    frontend
)

target_compile_definitions(ir_generation PRIVATE ${LLVM_DEFINITIONS})

# Optimization module
add_library(optimization
    optimization/Optimizer.cpp
)

target_include_directories(optimization PUBLIC
    optimization
    ${LLVM_INCLUDE_DIRS}
)

target_link_libraries(optimization
    ${llvm_libs}
)

target_compile_definitions(optimization PRIVATE ${LLVM_DEFINITIONS})

# ARM Code Generation module
add_library(arm_codegen
    codegen/ARMCodeGenerator.cpp
)

target_include_directories(arm_codegen PUBLIC
    codegen
    ${LLVM_INCLUDE_DIRS}
)

target_link_libraries(arm_codegen
    ${llvm_libs}
)

target_compile_definitions(arm_codegen PRIVATE ${LLVM_DEFINITIONS})

