#include "IRGenerator.hpp"
#include "Type.hpp"
#include <iostream>
#include "llvm/IR/BasicBlock.h"
#include "llvm/IR/Constants.h"
#include "llvm/IR/Instructions.h"
#include "llvm/IR/Type.h"
#include "llvm/IR/Value.h"
#include "llvm/Support/Casting.h"
#include <vector>

IRGenerator::IRGenerator(llvm::LLVMContext* context, llvm::Module* module, llvm::IRBuilder<>* builder)
    : llvm_context(context), llvm_module(module), llvm_builder(builder) {
    // Constructor now just stores references to LLVM components
    // Runtime library initialization is done separately
}

void IRGenerator::printLLVMIR() {
    llvm_module->print(llvm::outs(), nullptr);
}

void IRGenerator::printIndent() {
    for (int i = 0; i < indent; i++) std::cout << "  ";
}

// Initialize SysY runtime library functions (external declarations only)
// Based on SysY 2022 official specification
void IRGenerator::initializeRuntimeLibrary() {
    std::cout << "Declaring SysY 2022 runtime library functions..." << std::endl;

    // === SysY 2022 Official I/O Functions ===

    // 1) int getint() - 输入一个整数，返回对应的整数值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getint", llvm_module);
        function_table["getint"] = func;
    }

    // 2) int getch() - 输入一个字符，返回字符对应的 ASCII 码值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getch", llvm_module);
        function_table["getch"] = func;
    }

    // 3) float getfloat() - 输入一个浮点数，返回对应的浮点数值
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getFloatTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getfloat", llvm_module);
        function_table["getfloat"] = func;
    }

    // 4) int getarray(int[]) - 输入一串整数，第1个整数代表后续输入的整数个数，返回整数个数
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getInt32Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getarray", llvm_module);
        function_table["getarray"] = func;
    }

    // 5) int getfarray(float[]) - 输入一个整数后再输入若干个浮点数，返回浮点数个数
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getFloatTy(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getInt32Ty(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "getfarray", llvm_module);
        function_table["getfarray"] = func;
    }

    // 6) void putint(int) - 输出一个整数的值
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putint", llvm_module);
        function_table["putint"] = func;
    }

    // 7) void putch(int) - 输出一个字符，参数为字符的 ASCII 码值
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putch", llvm_module);
        function_table["putch"] = func;
    }

    // 8) void putfloat(float) - 输出一个浮点数的值
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getFloatTy(*llvm_context)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putfloat", llvm_module);
        function_table["putfloat"] = func;
    }

    // 9) void putarray(int, int[]) - 输出一个整数数组，第1个参数为数组长度，第2个参数为数组
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context),
            llvm::PointerType::get(llvm::Type::getInt32Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putarray", llvm_module);
        function_table["putarray"] = func;
    }

    // 10) void putfarray(int, float[]) - 输出一个浮点数数组，第1个参数为数组长度，第2个参数为数组
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::Type::getInt32Ty(*llvm_context),
            llvm::PointerType::get(llvm::Type::getFloatTy(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putfarray", llvm_module);
        function_table["putfarray"] = func;
    }


    // 11) void putf(format, ...) - 格式化输出 (variadic function)
    {
        std::vector<llvm::Type*> paramTypes = {
            llvm::PointerType::get(llvm::Type::getInt8Ty(*llvm_context), 0)
        };
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, true); // variadic
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "putf", llvm_module);
        function_table["putf"] = func;
    }

    // === SysY 2022 Official Timer Functions ===

    // 12) void starttime() - 计时开始，参数为计时点编号
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "_sysy_starttime", llvm_module);
        function_table["starttime"] = func;
    }

    // 13) void stoptime() - 计时结束，参数为计时点编号
    {
        std::vector<llvm::Type*> paramTypes = {};
        llvm::FunctionType* funcType = llvm::FunctionType::get(llvm::Type::getVoidTy(*llvm_context), paramTypes, false);
        llvm::Function* func = llvm::Function::Create(funcType, llvm::Function::ExternalLinkage, "_sysy_stoptime", llvm_module);
        function_table["stoptime"] = func;
    }

    std::cout << "SysY 2022 runtime library functions declared successfully." << std::endl;
}

// Scope management helpers
void IRGenerator::pushScope() {
    // Save current symbol table state
    scope_stack.push_back(named_values);
}

void IRGenerator::popScope() {
    if (!scope_stack.empty()) {
        // Restore previous symbol table state
        named_values = scope_stack.back();
        scope_stack.pop_back();
    } else {
        std::cerr << "Error: popScope() called without matching pushScope()" << std::endl;
    }
}

// Loop context management helpers
void IRGenerator::pushLoop(llvm::BasicBlock* break_target, llvm::BasicBlock* continue_target, const std::string& loop_name) {
    loop_stack.push_back({break_target, continue_target, loop_name});
}

void IRGenerator::popLoop() {
    if (!loop_stack.empty()) {
        loop_stack.pop_back();
    } else {
        std::cerr << "Error: popLoop() called without matching pushLoop()" << std::endl;
    }
}

llvm::BasicBlock* IRGenerator::getCurrentBreakTarget() {
    if (!loop_stack.empty()) {
        return loop_stack.back().break_target;
    } else {
        std::cerr << "Error: getCurrentBreakTarget() called outside of loop" << std::endl;
    }
    return nullptr;
}

llvm::BasicBlock* IRGenerator::getCurrentContinueTarget() {
    if (!loop_stack.empty()) {
        return loop_stack.back().continue_target;
    } else {
        std::cerr << "Error: getCurrentContinueTarget() called outside of loop" << std::endl;
    }
    return nullptr;
}

// Helper function to create zero-initialized array constants
llvm::Constant* IRGenerator::createZeroInitializedArray(llvm::Type* baseType, const std::vector<int>& dimensions) {
    if (dimensions.empty()) {
        // Scalar zero value
        if (baseType->isIntegerTy()) {
            return llvm::ConstantInt::get(baseType, 0);
        } else if (baseType->isFloatTy()) {
            return llvm::ConstantFP::get(baseType, 0.0);
        }
        return llvm::Constant::getNullValue(baseType);
    }

    // Array zero value
    llvm::Type* currentType = baseType;
    for (int i = dimensions.size() - 1; i >= 0; i--) {
        currentType = llvm::ArrayType::get(currentType, dimensions[i]);
    }

    return llvm::Constant::getNullValue(currentType);
}

// Helper function to create partially initialized array constants
llvm::Constant* IRGenerator::createPartiallyInitializedArray(
    llvm::Type* baseType,
    const std::vector<int>& dimensions,
    const std::vector<llvm::Constant*>& providedElements) {

    if (dimensions.empty() || providedElements.empty()) {
        return createZeroInitializedArray(baseType, dimensions);
    }

    // For 1D arrays, handle partial initialization
    if (dimensions.size() == 1) {
        int arraySize = dimensions[0];
        int providedSize = providedElements.size();

        // Check for oversized initializer
        if (providedSize > arraySize) {
            std::cerr << "Error: Too many initializers for array of size " << arraySize
                      << " (got " << providedSize << ")" << std::endl;
            return nullptr;
        }

        // Create array with proper size, zero-padding missing elements
        std::vector<llvm::Constant*> fullElements;

        // Add provided elements
        for (int i = 0; i < providedSize; i++) {
            fullElements.push_back(providedElements[i]);
        }

        // Zero-pad remaining elements
        llvm::Constant* zeroElement;
        if (baseType->isIntegerTy()) {
            zeroElement = llvm::ConstantInt::get(baseType, 0);
        } else if (baseType->isFloatTy()) {
            zeroElement = llvm::ConstantFP::get(baseType, 0.0);
        } else {
            zeroElement = llvm::Constant::getNullValue(baseType);
        }

        for (int i = providedSize; i < arraySize; i++) {
            fullElements.push_back(zeroElement);
        }

        llvm::ArrayType* arrayType = llvm::ArrayType::get(baseType, arraySize);
        return llvm::ConstantArray::get(arrayType, fullElements);
    }

    // For multi-dimensional arrays (2D, 3D, etc.)
    if (dimensions.size() == 2) {
        // Handle 2D arrays: int matrix[rows][cols] = {{...}, {...}, ...}

        return createPartiallyInitialized2DArray(baseType, dimensions, providedElements);
    } else if (dimensions.size() == 3) {
        // Handle 3D arrays: int cube[d1][d2][d3] = {{{...}}, {{...}}, ...}

        return createPartiallyInitialized3DArray(baseType, dimensions, providedElements);
    } else {
        // For higher dimensions, fall back to zero init for now
        std::cerr << "Warning: " << dimensions.size() << "D partial initialization not implemented" << std::endl;
        return createZeroInitializedArray(baseType, dimensions);
    }
}

// Helper function for 2D partial initialization
llvm::Constant* IRGenerator::createPartiallyInitialized2DArray(
    llvm::Type* baseType,
    const std::vector<int>& dimensions,
    const std::vector<llvm::Constant*>& providedElements) {

    if (dimensions.size() != 2) {
        std::cerr << "Error: createPartiallyInitialized2DArray called with non-2D dimensions" << std::endl;
        return nullptr;
    }

    int rows = dimensions[0];
    int cols = dimensions[1];
    int providedRows = providedElements.size();

    // Check for oversized initialization
    if (providedRows > rows) {
        std::cerr << "Error: Too many rows for 2D array of size [" << rows << "][" << cols
                  << "] (got " << providedRows << " rows)" << std::endl;
        return nullptr;
    }

    std::vector<llvm::Constant*> rowConstants;

    // Process provided rows
    for (int r = 0; r < providedRows; r++) {
        llvm::Constant* rowElement = providedElements[r];



        if (auto rowArray = llvm::dyn_cast<llvm::ConstantArray>(rowElement)) {
            // This row is an array constant
            llvm::ArrayType* rowArrayType = rowArray->getType();
            unsigned providedCols = rowArrayType->getNumElements();



            if (providedCols > (unsigned)cols) {
                std::cerr << "Error: Too many columns in row " << r << " for 2D array [" << rows
                          << "][" << cols << "] (got " << providedCols << " columns)" << std::endl;
                return nullptr;
            } else if (providedCols < (unsigned)cols) {
                // Partial row - need to expand with zeros
                std::vector<llvm::Constant*> expandedRowElements;

                // Add provided elements
                for (unsigned c = 0; c < providedCols; c++) {
                    expandedRowElements.push_back(rowArray->getAggregateElement(c));
                }

                // Zero-pad remaining columns
                llvm::Constant* zeroElement;
                if (baseType->isIntegerTy()) {
                    zeroElement = llvm::ConstantInt::get(baseType, 0);
                } else if (baseType->isFloatTy()) {
                    zeroElement = llvm::ConstantFP::get(baseType, 0.0);
                } else {
                    zeroElement = llvm::Constant::getNullValue(baseType);
                }

                for (int c = providedCols; c < cols; c++) {
                    expandedRowElements.push_back(zeroElement);
                }

                // Create expanded row array
                llvm::ArrayType* expandedRowType = llvm::ArrayType::get(baseType, cols);
                rowConstants.push_back(llvm::ConstantArray::get(expandedRowType, expandedRowElements));
            } else {
                // Exact size row
                rowConstants.push_back(rowElement);
            }
        } else if (auto rowDataArray = llvm::dyn_cast<llvm::ConstantDataArray>(rowElement)) {
            // Handle ConstantDataArray (LLVM's optimized array representation)
            unsigned providedCols = rowDataArray->getNumElements();

            if (providedCols > (unsigned)cols) {
                std::cerr << "Error: Too many columns in row " << r << " for 2D array [" << rows
                          << "][" << cols << "] (got " << providedCols << " columns)" << std::endl;
                return nullptr;
            } else if (providedCols < (unsigned)cols) {
                // Partial row - need to expand with zeros
                std::vector<llvm::Constant*> expandedRowElements;

                // Add provided elements from ConstantDataArray
                for (unsigned c = 0; c < providedCols; c++) {
                    expandedRowElements.push_back(rowDataArray->getElementAsConstant(c));
                }

                // Zero-pad remaining columns
                llvm::Constant* zeroElement;
                if (baseType->isIntegerTy()) {
                    zeroElement = llvm::ConstantInt::get(baseType, 0);
                } else if (baseType->isFloatTy()) {
                    zeroElement = llvm::ConstantFP::get(baseType, 0.0);
                } else {
                    zeroElement = llvm::Constant::getNullValue(baseType);
                }

                for (int c = providedCols; c < cols; c++) {
                    expandedRowElements.push_back(zeroElement);
                }

                // Create expanded row array
                llvm::ArrayType* expandedRowType = llvm::ArrayType::get(baseType, cols);
                rowConstants.push_back(llvm::ConstantArray::get(expandedRowType, expandedRowElements));
            } else {
                // Exact size row - convert ConstantDataArray to ConstantArray
                std::vector<llvm::Constant*> rowElements;
                for (unsigned c = 0; c < providedCols; c++) {
                    rowElements.push_back(rowDataArray->getElementAsConstant(c));
                }
                llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, cols);
                rowConstants.push_back(llvm::ConstantArray::get(rowType, rowElements));
            }
        } else {
            // Element is not a ConstantArray or ConstantDataArray
            // Treat as single element in first position of row
            std::vector<llvm::Constant*> rowElements;
            rowElements.push_back(rowElement);

            // Zero-pad remaining columns
            llvm::Constant* zeroElement;
            if (baseType->isIntegerTy()) {
                zeroElement = llvm::ConstantInt::get(baseType, 0);
            } else if (baseType->isFloatTy()) {
                zeroElement = llvm::ConstantFP::get(baseType, 0.0);
            } else {
                zeroElement = llvm::Constant::getNullValue(baseType);
            }

            for (int c = 1; c < cols; c++) {
                rowElements.push_back(zeroElement);
            }

            llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, cols);
            rowConstants.push_back(llvm::ConstantArray::get(rowType, rowElements));
        }
    }

    // Zero-pad remaining rows
    if (providedRows < rows) {
        llvm::Constant* zeroElement;
        if (baseType->isIntegerTy()) {
            zeroElement = llvm::ConstantInt::get(baseType, 0);
        } else if (baseType->isFloatTy()) {
            zeroElement = llvm::ConstantFP::get(baseType, 0.0);
        } else {
            zeroElement = llvm::Constant::getNullValue(baseType);
        }

        // Create zero row
        std::vector<llvm::Constant*> zeroRowElements(cols, zeroElement);
        llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, cols);
        llvm::Constant* zeroRow = llvm::ConstantArray::get(rowType, zeroRowElements);

        for (int r = providedRows; r < rows; r++) {
            rowConstants.push_back(zeroRow);
        }
    }

    // Create the final 2D array
    llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, cols);
    llvm::ArrayType* matrixType = llvm::ArrayType::get(rowType, rows);
    return llvm::ConstantArray::get(matrixType, rowConstants);
}

// Helper function for 3D partial initialization
llvm::Constant* IRGenerator::createPartiallyInitialized3DArray(
    llvm::Type* baseType,
    const std::vector<int>& dimensions,
    const std::vector<llvm::Constant*>& providedElements) {

    if (dimensions.size() != 3) {
        std::cerr << "Error: createPartiallyInitialized3DArray called with non-3D dimensions" << std::endl;
        return nullptr;
    }

    int d1 = dimensions[0];  // First dimension
    int d2 = dimensions[1];  // Second dimension
    int d3 = dimensions[2];  // Third dimension
    int providedSlices = providedElements.size();

    // Check for oversized initialization
    if (providedSlices > d1) {
        std::cerr << "Error: Too many slices for 3D array of size [" << d1 << "][" << d2
                  << "][" << d3 << "] (got " << providedSlices << " slices)" << std::endl;
        return nullptr;
    }

    std::vector<llvm::Constant*> sliceConstants;

    // Process provided slices (each slice is a 2D array)
    for (int s = 0; s < providedSlices; s++) {
        llvm::Constant* sliceElement = providedElements[s];



        if (auto sliceArray = llvm::dyn_cast<llvm::ConstantArray>(sliceElement)) {

            // This slice is a 2D array constant
            llvm::ArrayType* sliceArrayType = sliceArray->getType();
            unsigned providedRows = sliceArrayType->getNumElements();

            if (providedRows > (unsigned)d2) {
                std::cerr << "Error: Too many rows in slice " << s << " for 3D array [" << d1
                          << "][" << d2 << "][" << d3 << "] (got " << providedRows << " rows)" << std::endl;
                return nullptr;
            } else if (providedRows < (unsigned)d2) {
                // Partial slice - need to expand with zero rows
                std::vector<llvm::Constant*> expandedSliceRows;

                // Add provided rows (need to handle partial rows within each slice)
                for (unsigned r = 0; r < providedRows; r++) {
                    llvm::Constant* rowElement = sliceArray->getAggregateElement(r);

                    // Check if this row needs expansion (similar to 2D logic)
                    if (auto rowArray = llvm::dyn_cast<llvm::ConstantArray>(rowElement)) {
                        // Handle ConstantArray rows
                        unsigned providedCols = rowArray->getType()->getNumElements();
                        if (providedCols < (unsigned)d3) {
                            // Partial row - expand with zeros
                            std::vector<llvm::Constant*> expandedRowElements;
                            for (unsigned c = 0; c < providedCols; c++) {
                                expandedRowElements.push_back(rowArray->getAggregateElement(c));
                            }
                            // Zero-pad remaining columns
                            llvm::Constant* zeroElement = baseType->isIntegerTy() ?
                                llvm::ConstantInt::get(baseType, 0) : llvm::ConstantFP::get(baseType, 0.0);
                            for (int c = providedCols; c < d3; c++) {
                                expandedRowElements.push_back(zeroElement);
                            }
                            llvm::ArrayType* expandedRowType = llvm::ArrayType::get(baseType, d3);
                            expandedSliceRows.push_back(llvm::ConstantArray::get(expandedRowType, expandedRowElements));
                        } else {
                            expandedSliceRows.push_back(rowElement);
                        }
                    } else if (auto rowDataArray = llvm::dyn_cast<llvm::ConstantDataArray>(rowElement)) {
                        // Handle ConstantDataArray rows (LLVM's optimized representation)
                        unsigned providedCols = rowDataArray->getNumElements();
                        if (providedCols < (unsigned)d3) {
                            // Partial row - expand with zeros
                            std::vector<llvm::Constant*> expandedRowElements;
                            for (unsigned c = 0; c < providedCols; c++) {
                                expandedRowElements.push_back(rowDataArray->getElementAsConstant(c));
                            }
                            // Zero-pad remaining columns
                            llvm::Constant* zeroElement = baseType->isIntegerTy() ?
                                llvm::ConstantInt::get(baseType, 0) : llvm::ConstantFP::get(baseType, 0.0);
                            for (int c = providedCols; c < d3; c++) {
                                expandedRowElements.push_back(zeroElement);
                            }
                            llvm::ArrayType* expandedRowType = llvm::ArrayType::get(baseType, d3);
                            expandedSliceRows.push_back(llvm::ConstantArray::get(expandedRowType, expandedRowElements));
                        } else {
                            // Exact size row - convert ConstantDataArray to ConstantArray
                            std::vector<llvm::Constant*> rowElements;
                            for (unsigned c = 0; c < providedCols; c++) {
                                rowElements.push_back(rowDataArray->getElementAsConstant(c));
                            }
                            llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
                            expandedSliceRows.push_back(llvm::ConstantArray::get(rowType, rowElements));
                        }
                    } else {
                        // Single element or other type - treat as first element of row
                        std::vector<llvm::Constant*> rowElements;
                        rowElements.push_back(rowElement);
                        // Zero-pad remaining columns
                        llvm::Constant* zeroElement = baseType->isIntegerTy() ?
                            llvm::ConstantInt::get(baseType, 0) : llvm::ConstantFP::get(baseType, 0.0);
                        for (int c = 1; c < d3; c++) {
                            rowElements.push_back(zeroElement);
                        }
                        llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
                        expandedSliceRows.push_back(llvm::ConstantArray::get(rowType, rowElements));
                    }
                }

                // Create zero row for padding
                llvm::Constant* zeroElement;
                if (baseType->isIntegerTy()) {
                    zeroElement = llvm::ConstantInt::get(baseType, 0);
                } else if (baseType->isFloatTy()) {
                    zeroElement = llvm::ConstantFP::get(baseType, 0.0);
                } else {
                    zeroElement = llvm::Constant::getNullValue(baseType);
                }

                std::vector<llvm::Constant*> zeroRowElements(d3, zeroElement);
                llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
                llvm::Constant* zeroRow = llvm::ConstantArray::get(rowType, zeroRowElements);

                // Zero-pad remaining rows
                for (int r = providedRows; r < d2; r++) {
                    expandedSliceRows.push_back(zeroRow);
                }

                // Create expanded slice
                llvm::ArrayType* expandedSliceType = llvm::ArrayType::get(rowType, d2);
                sliceConstants.push_back(llvm::ConstantArray::get(expandedSliceType, expandedSliceRows));
            } else {
                // Exact size slice
                sliceConstants.push_back(sliceElement);
            }
        } else {
            // Single element or malformed - create a slice with this element in [0][0][0] position


            // Create zero element
            llvm::Constant* zeroElement;
            if (baseType->isIntegerTy()) {
                zeroElement = llvm::ConstantInt::get(baseType, 0);
            } else if (baseType->isFloatTy()) {
                zeroElement = llvm::ConstantFP::get(baseType, 0.0);
            } else {
                zeroElement = llvm::Constant::getNullValue(baseType);
            }

            // Create slice with single element at [0][0] and rest zeros
            std::vector<llvm::Constant*> sliceRows;
            for (int r = 0; r < d2; r++) {
                std::vector<llvm::Constant*> rowElements;
                for (int c = 0; c < d3; c++) {
                    if (r == 0 && c == 0) {
                        rowElements.push_back(sliceElement);
                    } else {
                        rowElements.push_back(zeroElement);
                    }
                }
                llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
                sliceRows.push_back(llvm::ConstantArray::get(rowType, rowElements));
            }

            llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
            llvm::ArrayType* sliceType = llvm::ArrayType::get(rowType, d2);
            sliceConstants.push_back(llvm::ConstantArray::get(sliceType, sliceRows));
        }
    }

    // Zero-pad remaining slices
    if (providedSlices < d1) {
        llvm::Constant* zeroElement;
        if (baseType->isIntegerTy()) {
            zeroElement = llvm::ConstantInt::get(baseType, 0);
        } else if (baseType->isFloatTy()) {
            zeroElement = llvm::ConstantFP::get(baseType, 0.0);
        } else {
            zeroElement = llvm::Constant::getNullValue(baseType);
        }

        // Create zero slice
        std::vector<llvm::Constant*> zeroSliceRows;
        for (int r = 0; r < d2; r++) {
            std::vector<llvm::Constant*> zeroRowElements(d3, zeroElement);
            llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
            zeroSliceRows.push_back(llvm::ConstantArray::get(rowType, zeroRowElements));
        }

        llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
        llvm::ArrayType* sliceType = llvm::ArrayType::get(rowType, d2);
        llvm::Constant* zeroSlice = llvm::ConstantArray::get(sliceType, zeroSliceRows);

        for (int s = providedSlices; s < d1; s++) {
            sliceConstants.push_back(zeroSlice);
        }
    }

    // Create the final 3D array
    llvm::ArrayType* rowType = llvm::ArrayType::get(baseType, d3);
    llvm::ArrayType* sliceType = llvm::ArrayType::get(rowType, d2);
    llvm::ArrayType* cubeType = llvm::ArrayType::get(sliceType, d1);
    return llvm::ConstantArray::get(cubeType, sliceConstants);
}

// AST visitor methods implementation

antlrcpp::Any IRGenerator::visitProgram(SysY2022Parser::ProgramContext *ctx) {
    printIndent();
    std::cout << "Program (" << std::endl;
    indent++;

    // Visit all compilation units
    for (auto compUnit : ctx->compUnit()) {
        visitCompUnit(compUnit);
    }

    indent--;
    printIndent();
    std::cout << ") End Program" << std::endl;
    return nullptr;
}

/*
compUnit
    : decl | funcDef;
*/
antlrcpp::Any IRGenerator::visitCompUnit(SysY2022Parser::CompUnitContext *ctx) {
    printIndent();
    std::cout << "CompUnit (" << std::endl;
    indent++;

    if (ctx->decl()) {
        visitDecl(ctx->decl());
    } else if (ctx->funcDef()) {
        visitFuncDef(ctx->funcDef());
    }

    indent--;
    printIndent();
    std::cout << ") End CompUnit" << std::endl;
    return nullptr;
}

/*
decl : constDecl | varDecl;
*/
antlrcpp::Any IRGenerator::visitDecl(SysY2022Parser::DeclContext *ctx) {
    printIndent();
    indent++;
    if (ctx->varDecl()) {
        std::cout << "Var Decl" << std::endl;
        visitVarDecl(ctx->varDecl());
    } else if (ctx->constDecl()) {
        std::cout << "Const Decl" << std::endl;
        visitConstDecl(ctx->constDecl());
    } else {
        std::cerr << "Unknown declaration: " << ctx->getText() << std::endl;
    }

    indent--;
    return nullptr;
}

/*
constDecl : CONST bType constDef ( COMMA constDef )* SEMICOLON;
*/
antlrcpp::Any IRGenerator::visitConstDecl(SysY2022Parser::ConstDeclContext *ctx) {
    printIndent();
    std::cout << "ConstDecl" << std::endl;
    indent++;
    // get type
    this->current_type = get_llvm_type(ctx->bType(), *llvm_context);
    // visit constDef
    for (auto const_def : ctx->constDef()) {
        visitConstDef(const_def);
    }
    this->current_type = nullptr;
    indent--;
    return nullptr;
}

/*
bType: INT | FLOAT;
*/
antlrcpp::Any IRGenerator::visitBType(SysY2022Parser::BTypeContext *ctx) {
    printIndent();
    std::cout << "BType: " << ctx->getText() << std::endl;

    if (ctx->INT()) {
        return llvm::Type::getInt32Ty(*llvm_context);
    } else if (ctx->FLOAT()) {
        return llvm::Type::getFloatTy(*llvm_context);
    } else {
        std::cerr << "Unknown type: " << ctx->getText() << std::endl;
    }

    return llvm::Type::getInt32Ty(*llvm_context); // fallback
}

/*
constDef : IDENT ( LBRACKET constExp RBRACKET )* ASSIGN constInitVal;
*/
antlrcpp::Any IRGenerator::visitConstDef(SysY2022Parser::ConstDefContext *ctx) {
    printIndent();
    indent++;
    std::string const_name = ctx->IDENT()->getText();
    std::cout << "ConstDef: " << const_name << " (" << std::endl;

    // array dimensions
    std::vector<int> dimensions;
    if (!ctx->LBRACKET().empty()) {
        for (auto constExp : ctx->constExp()) {
            llvm::Value* sizeValue = visitConstExp(constExp);
            if (auto constInt = llvm::dyn_cast<llvm::ConstantInt>(sizeValue)) {
                dimensions.push_back(constInt->getZExtValue());
            } else {
                std::cerr << "Array size is not a constant integer" << std::endl;
                // fall back to 1 dimension in this layer
                dimensions.push_back(1);
            }
        }

        // create array type
        llvm::Type* arrayType = get_llvm_type((((SysY2022Parser::ConstDeclContext*)ctx->parent)->bType()), *llvm_context);
        for (int i = dimensions.size() - 1; i >= 0; i--) {
            arrayType = llvm::ArrayType::get(arrayType, dimensions[i]);
        }

        // Process array initialization
        if (ctx->constInitVal()) {
            std::cout << " = " << ctx->constInitVal()->getText() << std::endl;
            llvm::Value* initValue = visitConstInitVal(ctx->constInitVal());

            llvm::Constant* constArray = nullptr;
            if (initValue) {
                // Non-empty initialization
                constArray = llvm::dyn_cast<llvm::Constant>(initValue);
            } else {
                // Empty initialization: const int a[5] = {};
                std::cout << "Creating zero-initialized constant array" << std::endl;
                constArray = createZeroInitializedArray(current_type, dimensions);
            }

            if (constArray) {
                if (current_function) {
                    // Local constant array - use alloca
                    llvm::AllocaInst* alloca = llvm_builder->CreateAlloca(arrayType, nullptr, const_name);
                    llvm_builder->CreateStore(constArray, alloca);

                    std::vector<llvm::Value*> value_list = {alloca};
                    named_values[const_name] = value_list;
                } else {
                    // Global constant array
                    llvm::GlobalVariable* globalArray = new llvm::GlobalVariable(
                        *llvm_module, arrayType, true, // isConstant = true
                        llvm::GlobalValue::InternalLinkage, constArray, const_name);

                    std::vector<llvm::Value*> value_list = {globalArray};
                    named_values[const_name] = value_list;
                }
            }
        }
    } else {
        // Scalar constant
        if (ctx->constInitVal()) {
            std::cout << " = " << ctx->constInitVal()->getText() << std::endl;
            llvm::Value* initValue = visitConstInitVal(ctx->constInitVal());

            if (auto constValue = llvm::dyn_cast<llvm::Constant>(initValue)) {
                if (current_function) {
                    // Local scalar constant - store directly in symbol table
                    std::vector<llvm::Value*> value_list = {constValue};
                    named_values[const_name] = value_list;
                } else {
                    // Global scalar constant
                    llvm::GlobalVariable* globalConst = new llvm::GlobalVariable(
                        *llvm_module, current_type, true, // isConstant = true
                        llvm::GlobalValue::InternalLinkage, constValue, const_name);

                    std::vector<llvm::Value*> value_list = {globalConst};
                    named_values[const_name] = value_list;
                }
            }
        } else {
            std::cout << std::endl;
            std::cerr << "Constant must have initialization value" << std::endl;
        }
    }
    indent--;
    printIndent();
    std::cout << ") End ConstDef for " << const_name;
    for (int i = 0; i < dimensions.size(); i++) {
        std::cout << "[" << dimensions[i] << "]";
    }
    std::cout << std::endl;
    return nullptr;
}

/*
constInitVal
    : constExp
    | LBRACE ( constInitVal ( COMMA constInitVal )* )? RBRACE;
*/
// TODO: lack of logic
antlrcpp::Any IRGenerator::visitConstInitVal(SysY2022Parser::ConstInitValContext *ctx) {
    printIndent();
    indent++;
    llvm::Value *llvm_constInitVal = nullptr;

    if (ctx->constExp()) {
        // Single value initialization: const int a = 5;
        std::cout << "ConstInitVal (single value) (" << std::endl;
        llvm_constInitVal = visitConstExp(ctx->constExp());
    } else if (ctx->LBRACE()) {
        // Array initialization: const int a[3] = {1, 2, 3}; or const int a[3] = {};
        std::cout << "ConstInitVal (array) {" << std::endl;

        std::vector<llvm::Value*> elements;
        for (auto constInitVal : ctx->constInitVal()) {
            llvm::Value* element = visitConstInitVal(constInitVal);
            if (element) {
                elements.push_back(element);
            }
        }

        if (!elements.empty()) {
            // Non-empty initialization
            // For now, return the first element as a placeholder
            // Full array constant creation would require more complex logic
            llvm_constInitVal = elements[0];
        } else {
            // Empty initialization: {}
            // Return nullptr to signal empty initialization
            std::cout << "Empty array initialization detected" << std::endl;
            llvm_constInitVal = nullptr;
        }

        std::cout << "}" << std::endl;
    } else {
        std::cerr << "Unknown constInitVal format" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End ConstInitVal" << std::endl;
    return llvm_constInitVal;
}

/*
constExp : addExp;
*/
antlrcpp::Any IRGenerator::visitConstExp(SysY2022Parser::ConstExpContext *ctx) {
    printIndent();
    indent++;
    llvm::Value* llvm_constExp = nullptr;
    std::cout << "ConstExp (" << std::endl;

    if (ctx->addExp()) {
        llvm_constExp = visitAddExp(ctx->addExp());
    } else {
        std::cerr << "Unknown constExp format" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End ConstExp" << std::endl;
    return llvm_constExp;
}

/*
number : INTCONST | FLOATCONST;
*/
antlrcpp::Any IRGenerator::visitNumber(SysY2022Parser::NumberContext *ctx) {
    llvm::Value* llvm_number = nullptr;

    if (ctx->INTCONST()) {
        std::string text = ctx->INTCONST()->getText();
        int value;

        if (text.substr(0, 2) == "0x" || text.substr(0, 2) == "0X") {
            // Hexadecimal
            value = std::stoi(text, nullptr, 16);
            printIndent();
            std::cout << "Number (Hex): " << text << " = " << value << std::endl;
        } else if (text[0] == '0' && text.length() > 1) {
            // Octal
            value = std::stoi(text, nullptr, 8);
            printIndent();
            std::cout << "Number (Oct): " << text << " = " << value << std::endl;
        } else {
            // Decimal
            value = std::stoi(text);
            printIndent();
            std::cout << "Number (Dec): " << text << " = " << value << std::endl;
        }

        llvm_number = llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), value);
    } else if (ctx->FLOATCONST()) {
        float value = std::stof(ctx->FLOATCONST()->getText());
        printIndent();
        std::cout << "Number (Float): " << ctx->FLOATCONST()->getText() << " = " << value << std::endl;
        llvm_number = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), value);
    } else {
        std::cerr << "Unknown number type: " << ctx->getText() << std::endl;
    }

    return llvm_number;
}

/*
lVal: IDENT ( LBRACKET exp RBRACKET )*;
*/
// TODO: check logic for global variable
antlrcpp::Any IRGenerator::visitLVal(SysY2022Parser::LValContext *ctx) {
    printIndent();
    std::cout << "LVal: ";
    std::string var_name = ctx->IDENT()->getText();
    llvm::Value* l_val = nullptr;

    // Check if variable exists in symbol table
    if (named_values.find(var_name) != named_values.end()) {
        llvm::Value* var = named_values[var_name][0];

        // Handle array indexing if present
        if (!ctx->exp().empty()) {
            // Array access: var[index1][index2]...
            std::cout << var_name << "[";

            // Start with the base pointer
            llvm::Value* ptr = var;

            // Build all indices for multi-dimensional array access
            std::vector<llvm::Value*> indices;
            indices.push_back(llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0)); // Base index

            for (size_t i = 0; i < ctx->exp().size(); i++) {
                llvm::Value* index = visitExp(ctx->exp(i));
                indices.push_back(index);
                if (i > 0) std::cout << "][";
                std::cout << "index";
            }
            std::cout << "]";

            // Single GEP operation with all indices
            llvm::Type* baseType = llvm::dyn_cast<llvm::AllocaInst>(ptr)->getAllocatedType();
            ptr = llvm_builder->CreateGEP(baseType, ptr, indices, "arrayidx");

            // Determine the final element type
            llvm::Type* elementType = baseType;
            for (size_t i = 0; i < ctx->exp().size(); i++) {
                if (auto arrayType = llvm::dyn_cast<llvm::ArrayType>(elementType)) {
                    elementType = arrayType->getElementType();
                } else {
                    std::cerr << "Error: Too many indices for array type" << std::endl;
                    break;
                }
            }

            // Load the value from the final pointer
            l_val = llvm_builder->CreateLoad(elementType, ptr, var_name + "_val");
        } else {
            // Scalar variable access or array name (for function arguments)
            // Check if it's a global constant
            if (auto global_var = llvm::dyn_cast<llvm::GlobalVariable>(var)) {
                // Check if it's an array type
                if (global_var->getValueType()->isArrayTy()) {
                    // For arrays, return pointer to first element
                    std::vector<llvm::Value*> indices = {
                        llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0),
                        llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0)
                    };
                    l_val = llvm_builder->CreateGEP(global_var->getValueType(), global_var, indices, var_name + "_ptr");
                } else {
                    l_val = llvm_builder->CreateLoad(global_var->getValueType(), global_var, var_name);
                }
            }
            // Check if it's a local alloca (variable or local constant array)
            else if (auto alloca_inst = llvm::dyn_cast<llvm::AllocaInst>(var)) {
                // Check if it's an array type
                if (alloca_inst->getAllocatedType()->isArrayTy()) {
                    // For arrays, return pointer to first element
                    std::vector<llvm::Value*> indices = {
                        llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0),
                        llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0)
                    };
                    l_val = llvm_builder->CreateGEP(alloca_inst->getAllocatedType(), alloca_inst, indices, var_name + "_ptr");
                } else {
                    l_val = llvm_builder->CreateLoad(alloca_inst->getAllocatedType(), alloca_inst, var_name);
                }
            }
            // Check if it's a constant value (scalar constants)
            else if (auto constValue = llvm::dyn_cast<llvm::Constant>(var)) {
                std::cerr << "Warning: Accessing constant as lvalue: " << var_name << std::endl;
                l_val = constValue; // Return constant directly
            }
            else {
                std::cerr << "Unknown variable type for: " << var_name << std::endl;
            }
        }
    } else {
        std::cerr << "Undefined variable: " << var_name << std::endl;
    }

    std::cout << var_name << std::endl;
    return l_val;
}

/*
exp : addExp;
*/
antlrcpp::Any IRGenerator::visitExp(SysY2022Parser::ExpContext *ctx) {
    printIndent();
    std::cout << "Expression (" << std::endl;
    indent++;

    llvm::Value* llvm_exp = nullptr;
    if (ctx->addExp()) {
        llvm_exp = visitAddExp(ctx->addExp());
    } else {
        std::cerr << "Unknown expression format" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End Expression" << std::endl;
    return llvm_exp;
}

/*
unaryOP : ADD | SUB | NOT;
*/
antlrcpp::Any IRGenerator::visitAddExp(SysY2022Parser::AddExpContext *ctx) {
    printIndent();
    std::cout << "AddExp (" << std::endl;
    indent++;

    llvm::Value* llvm_addExp = nullptr;

    if (ctx->addExp()) {
        // addExp ( ADD | SUB ) mulExp;
        llvm::Value *left = visitAddExp(ctx->addExp());
        llvm::Value *right = visitMulExp(ctx->mulExp());

        if (ctx->ADD()) {
            if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float if needed
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                llvm_addExp = llvm_builder->CreateFAdd(left, right, "addtmp");
            } else {
                llvm_addExp = llvm_builder->CreateAdd(left, right, "addtmp");
            }
        } else if (ctx->SUB()) {
            if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float if needed
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                llvm_addExp = llvm_builder->CreateFSub(left, right, "subtmp");
            } else {
                llvm_addExp = llvm_builder->CreateSub(left, right, "subtmp");
            }
        } else {
            std::cerr << "Unknown operator in AddExp" << std::endl;
        }
    } else if (ctx->mulExp()) {
        llvm_addExp = visitMulExp(ctx->mulExp());
    } else {
        std::cerr << "Unknown expression in AddExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End AddExp" << std::endl;
    return llvm_addExp;
}

/*
mulExp
    : unaryExp
    | mulExp ( MUL | DIV | MOD ) unaryExp;
*/
antlrcpp::Any IRGenerator::visitMulExp(SysY2022Parser::MulExpContext *ctx) {
    printIndent();
    std::cout << "MulExp (" << std::endl;
    indent++;

    llvm::Value* llvm_mulExp = nullptr;

    if (ctx->mulExp()) {
        // mulExp ( MUL | DIV | MOD ) unaryExp;
        llvm::Value *left = visitMulExp(ctx->mulExp());
        llvm::Value *right = visitUnaryExp(ctx->unaryExp());

        if (ctx->MUL()) {
            if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float if needed
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                llvm_mulExp = llvm_builder->CreateFMul(left, right, "multmp");
            } else {
                llvm_mulExp = llvm_builder->CreateMul(left, right, "multmp");
            }
        } else if (ctx->DIV()) {
            if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Convert to float if needed
                if (left->getType()->isIntegerTy()) {
                    left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                if (right->getType()->isIntegerTy()) {
                    right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                }
                llvm_mulExp = llvm_builder->CreateFDiv(left, right, "divtmp");
            } else {
                llvm_mulExp = llvm_builder->CreateSDiv(left, right, "divtmp");
            }
        } else if (ctx->MOD()) {
            if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                // Float modulo is not directly supported in LLVM, use fmod from math library
                std::cerr << "Warning: Float modulo not implemented, treating as integer" << std::endl;
                // For now, convert to int (this is not ideal but prevents crash)
                if (left->getType()->isFloatTy()) {
                    left = llvm_builder->CreateFPToSI(left, llvm::Type::getInt32Ty(*llvm_context), "float2int");
                }
                if (right->getType()->isFloatTy()) {
                    right = llvm_builder->CreateFPToSI(right, llvm::Type::getInt32Ty(*llvm_context), "float2int");
                }
                llvm_mulExp = llvm_builder->CreateSRem(left, right, "modtmp");
            } else {
                llvm_mulExp = llvm_builder->CreateSRem(left, right, "modtmp");
            }
        } else {
            std::cerr << "Unknown operator in MulExp" << std::endl;
        }
    } else if (ctx->unaryExp()) {
        llvm_mulExp = visitUnaryExp(ctx->unaryExp());
    } else {
        std::cerr << "Unknown expression in MulExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End MulExp" << std::endl;
    return llvm_mulExp;
}

/*
unaryExp
    : primaryExp
    | IDENT LPAREN funcRealParams? RPAREN
    | unaryOP unaryExp;
*/
antlrcpp::Any IRGenerator::visitUnaryExp(SysY2022Parser::UnaryExpContext *ctx) {
    printIndent();
    std::cout << "UnaryExp (" << std::endl;
    indent++;

    llvm::Value* llvm_unaryExp = nullptr;

    if (ctx->primaryExp()) {
        // primaryExp
        llvm_unaryExp = visitPrimaryExp(ctx->primaryExp());
    } else if (ctx->unaryOP()) {
        // unaryOP unaryExp
        llvm::Value* operand = visitUnaryExp(ctx->unaryExp());
        std::string op = visitUnaryOP(ctx->unaryOP());

        if (op == "+") {
            // Unary plus - no operation needed
            llvm_unaryExp = operand;
        } else if (op == "-") {
            // Unary minus
            if (operand->getType()->isIntegerTy()) {
                llvm_unaryExp = llvm_builder->CreateNeg(operand, "negtmp");
            } else if (operand->getType()->isFloatTy()) {
                llvm_unaryExp = llvm_builder->CreateFNeg(operand, "fnegtmp");
            }
        } else if (op == "!") {
            // Logical NOT
            if (operand->getType()->isIntegerTy(1)) {
                // Already boolean, just negate
                llvm_unaryExp = llvm_builder->CreateNot(operand, "nottmp");
            } else if (operand->getType()->isFloatTy()) {
                // Compare float with 0.0 and return true if equal (i.e., operand is 0.0)
                llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                llvm_unaryExp = llvm_builder->CreateFCmpOEQ(operand, zero, "fcmpeq");
            } else if (operand->getType()->isIntegerTy()) {
                // Compare integer with 0 and return true if equal (i.e., operand is 0)
                llvm::Value* zero = llvm::ConstantInt::get(operand->getType(), 0);
                llvm_unaryExp = llvm_builder->CreateICmpEQ(operand, zero, "icmpeq");
            }
        }
    } else if (ctx->IDENT()) {
        // Function call: IDENT LPAREN funcRealParams? RPAREN
        std::string func_name = ctx->IDENT()->getText();
        std::cout << "Function call: " << func_name << std::endl;

        // Look up function
        if (function_table.find(func_name) != function_table.end()) {
            llvm::Function* func = function_table[func_name];
            std::vector<llvm::Value*> args;

            if (ctx->funcRealParams()) {
                // Get function arguments
                auto argResult = visitFuncRealParams(ctx->funcRealParams());
                try {
                    auto argsPtr = argResult.as<std::shared_ptr<std::vector<llvm::Value*>>>();
                    args = *argsPtr;
                } catch (...) {
                    std::cerr << "Error: Failed to get function arguments" << std::endl;
                }
            }

            // Perform type conversion for function arguments if needed
            llvm::FunctionType* funcType = func->getFunctionType();
            for (size_t i = 0; i < args.size() && i < funcType->getNumParams(); i++) {
                llvm::Type* expectedType = funcType->getParamType(i);
                llvm::Value* arg = args[i];

                if (arg->getType() != expectedType) {
                    // Convert boolean (i1) to integer (i32) for functions like putint
                    if (arg->getType()->isIntegerTy(1) && expectedType->isIntegerTy(32)) {
                        args[i] = llvm_builder->CreateZExt(arg, expectedType, "bool2int");
                    }
                    // Convert float to int if needed
                    else if (arg->getType()->isFloatTy() && expectedType->isIntegerTy()) {
                        args[i] = llvm_builder->CreateFPToSI(arg, expectedType, "float2int");
                    }
                    // Convert int to float if needed
                    else if (arg->getType()->isIntegerTy() && expectedType->isFloatTy()) {
                        args[i] = llvm_builder->CreateSIToFP(arg, expectedType, "int2float");
                    }
                }
            }

            // Check if function returns void
            if (func->getReturnType()->isVoidTy()) {
                // Void function - don't assign a name to the call
                llvm_builder->CreateCall(func, args);
                llvm_unaryExp = nullptr; // Void functions don't return a value
            } else {
                // Non-void function - assign a name to the result
                llvm_unaryExp = llvm_builder->CreateCall(func, args, func_name + "_result");
            }
        } else {
            std::cerr << "Error: Unknown function '" << func_name << "'" << std::endl;
        }
    }

    indent--;
    printIndent();
    std::cout << ") End UnaryExp" << std::endl;
    return llvm_unaryExp;
}

/*
unaryOP : ADD | SUB | NOT;
*/
antlrcpp::Any IRGenerator::visitUnaryOP(SysY2022Parser::UnaryOPContext *ctx) {
    printIndent();
    std::cout << "UnaryOP: ";

    if (ctx->ADD()) {
        std::cout << "+" << std::endl;
        return std::string("+");
    } else if (ctx->SUB()) {
        std::cout << "-" << std::endl;
        return std::string("-");
    } else if (ctx->NOT()) {
        std::cout << "!" << std::endl;
        return std::string("!");
    }

    return std::string("");
}

/*
primaryExp : lVal | number |  LPAREN exp RPAREN;
*/
antlrcpp::Any IRGenerator::visitPrimaryExp(SysY2022Parser::PrimaryExpContext *ctx) {
    printIndent();
    std::cout << "PrimaryExp (" << std::endl;
    indent++;

    llvm::Value* llvm_primaryExp = nullptr;

    if (ctx->exp()) {
        // ( exp )
        llvm_primaryExp = visitExp(ctx->exp());
    } else if (ctx->lVal()) {
        // lVal
        llvm_primaryExp = visitLVal(ctx->lVal());
    } else if (ctx->number()) {
        // number
        llvm_primaryExp = visitNumber(ctx->number());
    }

    indent--;
    printIndent();
    std::cout << ") End PrimaryExp" << std::endl;
    return llvm_primaryExp;
}

/*
funcDef : funcType IDENT LPAREN ( funcFormalParams )? RPAREN block;
*/
antlrcpp::Any IRGenerator::visitFuncDef(SysY2022Parser::FuncDefContext *ctx) {
    std::string func_name = ctx->IDENT()->getText();

    printIndent();
    std::cout << "FuncDef: " << func_name << " (" << std::endl;
    indent++;

    // Get function return type
    // TODO: duplicated logic
    llvm::Type* return_type = nullptr;
    if (ctx->funcType()->bType()) {
        return_type = get_llvm_type(ctx->funcType()->bType(), *llvm_context);
        printIndent();
        std::cout << "FuncResultType: " << (ctx->funcType()->bType()->INT() ? "int" : "float") << std::endl;
    } else {
        return_type = llvm::Type::getVoidTy(*llvm_context);
        printIndent();
        std::cout << "FuncResultType: void" << std::endl;
    }

    // Collect parameter types and names
    std::vector<llvm::Type*> param_types;
    std::vector<std::string> param_names;

    if (ctx->funcFormalParams()) {
        // Visit parameters to collect types and names
        auto paramInfo = visitFuncFormalParams(ctx->funcFormalParams());
        try {
            auto params = paramInfo.as<std::pair<std::vector<llvm::Type*>, std::vector<std::string>>>();
            param_types = params.first;
            param_names = params.second;
        } catch (...) {
            std::cerr << "Error: Failed to get parameter information" << std::endl;
        }
    }

    // Create function type
    llvm::FunctionType* func_type = llvm::FunctionType::get(return_type, param_types, false);

    // Create function
    llvm::Function* function = llvm::Function::Create(
        func_type, llvm::Function::ExternalLinkage, func_name, llvm_module);

    // Store function in function table
    function_table[func_name] = function;

    // Set current function
    current_function = function;

    // Set parameter names
    auto argIt = function->arg_begin();
    for (size_t i = 0; i < param_names.size(); ++i, ++argIt) {
        argIt->setName(param_names[i]);
    }

    // Create entry basic block
    llvm::BasicBlock* entry_block = llvm::BasicBlock::Create(*llvm_context, "entry", function);
    llvm_builder->SetInsertPoint(entry_block);

    // Create allocas for parameters (to support assignment to parameters)
    argIt = function->arg_begin();
    for (size_t i = 0; i < param_names.size(); ++i, ++argIt) {
        llvm::AllocaInst* paramAlloca = llvm_builder->CreateAlloca(
            param_types[i], nullptr, param_names[i]);
        llvm_builder->CreateStore(&*argIt, paramAlloca);

        // Add to symbol table
        // TODO: check if save context is needed
        std::vector<llvm::Value*> value_list = {paramAlloca};
        named_values[param_names[i]] = value_list;

        printIndent();
        std::cout << "Parameter allocated: " << param_names[i] << std::endl;
    }

    printIndent();
    std::cout << "FuncFormalParams: "
              << (ctx->funcFormalParams() ? ctx->funcFormalParams()->getText() : "(void)") << std::endl;
    printIndent();
    std::cout << "Using BasicBlock: entry" << std::endl;

    // Visit function body
    if (ctx->block()) {
        visitBlock(ctx->block());
    } else {
        std::cerr << "Error: Function body not found" << std::endl;
    }

    // Add default return if function doesn't end with return
    // TODO: check if this is necessary
    if (!llvm_builder->GetInsertBlock()->getTerminator()) {
        if (return_type->isVoidTy()) {
            llvm_builder->CreateRetVoid();
        } else {
            // Return zero for non-void functions without explicit return
            if (return_type->isIntegerTy()) {
                llvm_builder->CreateRet(llvm::ConstantInt::get(return_type, 0));
            } else if (return_type->isFloatTy()) {
                llvm_builder->CreateRet(llvm::ConstantFP::get(return_type, 0.0));
            } else {
                llvm_builder->CreateRet(llvm::Constant::getNullValue(return_type));
            }
        }
    }

    // Reset current function
    current_function = nullptr;

    indent--;
    printIndent();
    std::cout << ") End FuncDef" << std::endl;
    return nullptr;
}

antlrcpp::Any IRGenerator::visitFuncType(SysY2022Parser::FuncTypeContext *ctx) {
    printIndent();
    std::cout << "FuncType: " << ctx->getText() << std::endl;

    if (ctx->bType()) {
        return get_llvm_type(ctx->bType(), *llvm_context);
    } else if (ctx->VOID()) {
        return llvm::Type::getVoidTy(*llvm_context);
    } else {
        std::cerr << "Unknown function type: " << ctx->getText() << std::endl;
    }

    return llvm::Type::getVoidTy(*llvm_context); // fallback
}

/*
block : LBRACE blockItem* RBRACE;
*/
// check if we need extra control flow for language block
antlrcpp::Any IRGenerator::visitBlock(SysY2022Parser::BlockContext *ctx) {
    printIndent();
    std::cout << "Block (" << std::endl;
    indent++;

    // Push new scope
    pushScope();

    // Visit all block items
    for (auto blockItem : ctx->blockItem()) {
        visitBlockItem(blockItem);
    }

    // Pop scope
    popScope();

    indent--;
    printIndent();
    std::cout << ") End Block" << std::endl;
    return nullptr;
}

/*
blockItem : decl | stmt;
*/
antlrcpp::Any IRGenerator::visitBlockItem(SysY2022Parser::BlockItemContext *ctx) {
    printIndent();
    std::cout << "BlockItem (" << std::endl;
    indent++;

    if (ctx->decl()) {
        visitDecl(ctx->decl());
    } else if (ctx->stmt()) {
        visitStmt(ctx->stmt());
    }

    indent--;
    printIndent();
    std::cout << ") End BlockItem" << std::endl;
    return nullptr;
}

// Missing visitor method implementations (now fully implemented)
/*
varDecl : bType varDef ( COMMA varDef )* SEMICOLON;
*/
antlrcpp::Any IRGenerator::visitVarDecl(SysY2022Parser::VarDeclContext *ctx) {
    printIndent();
    std::cout << "VarDecl (" << std::endl;
    indent++;

    // Get the base type from bType
    llvm::Type* baseType = get_llvm_type(ctx->bType(), *llvm_context);

    // Process each variable definition
    for (auto var_def : ctx->varDef()) {
        std::string var_name = var_def->IDENT()->getText();
        printIndent();
        std::cout << "Variable: " << var_name << std::endl;

        // Check for array dimensions
        std::vector<int> dimensions;
        for (auto constExp : var_def->constExp()) {
            llvm::Value* dimValue = visitConstExp(constExp);
            if (auto constInt = llvm::dyn_cast<llvm::ConstantInt>(dimValue)) {
                dimensions.push_back(constInt->getSExtValue());
            } else {
                std::cerr << "Error: Array dimension must be constant" << std::endl;
                dimensions.push_back(1); // fallback
            }
        }

        llvm::Type* varType = baseType;
        // Build array type from innermost to outermost
        for (int i = dimensions.size() - 1; i >= 0; i--) {
            varType = llvm::ArrayType::get(varType, dimensions[i]);
        }

        // Create alloca instruction
        llvm::AllocaInst* alloca = llvm_builder->CreateAlloca(varType, nullptr, var_name);

        // Store in symbol table
        if (named_values.find(var_name) != named_values.end()) {
            std::cerr << "Warning: Variable '" << var_name << "' redeclared" << std::endl;
        }
        std::vector<llvm::Value*> value_list = {alloca};
        named_values[var_name] = value_list;

        // Handle initialization if present
        if (var_def->initVal()) {
            printIndent();
            std::cout << "  Initialization: " << var_def->initVal()->getText() << std::endl;
            llvm::Value* initValue = visitInitVal(var_def->initVal());

            if (initValue) {
                // Non-empty initialization
                if (!varType->isArrayTy()) {
                    // Scalar initialization
                    llvm_builder->CreateStore(initValue, alloca);
                } else {
                    // Array initialization with brace syntax
                    if (auto constArray = llvm::dyn_cast<llvm::Constant>(initValue)) {
                        // Check if this is a partial initialization case
                        llvm::ArrayType* initArrayType = llvm::dyn_cast<llvm::ArrayType>(constArray->getType());
                        llvm::ArrayType* targetArrayType = llvm::dyn_cast<llvm::ArrayType>(varType);

                        if (initArrayType && targetArrayType) {
                            unsigned initSize = initArrayType->getNumElements();
                            unsigned targetSize = targetArrayType->getNumElements();

                            if (initSize > targetSize) {
                                // Oversized initialization - compile-time error
                                printIndent();
                                std::cerr << "  ERROR: Too many initializers for array of size " << targetSize
                                          << " (got " << initSize << " initializers)" << std::endl;
                                std::cout << "  ERROR: Too many initializers for array of size " << targetSize
                                          << " (got " << initSize << " initializers)" << std::endl;
                                // Still store the array to continue compilation, but with error
                                llvm_builder->CreateStore(constArray, alloca);
                            } else if (initSize < targetSize) {
                                // Partial initialization detected - need to expand with zeros
                                printIndent();
                                std::cout << "  Detected partial initialization: " << initSize
                                          << " elements provided for array of size " << targetSize << std::endl;

                                // Extract provided elements from the constant array
                                std::vector<llvm::Constant*> providedElements;
                                for (unsigned i = 0; i < initSize; i++) {
                                    llvm::Constant* elem = constArray->getAggregateElement(i);

                                    providedElements.push_back(elem);
                                }



                                // Create properly sized array with zero-padding
                                llvm::Constant* expandedArray = createPartiallyInitializedArray(
                                    baseType, dimensions, providedElements);

                                if (expandedArray) {
                                    llvm_builder->CreateStore(expandedArray, alloca);
                                    printIndent();
                                    std::cout << "  Stored expanded array with zero-padding" << std::endl;
                                } else {
                                    printIndent();
                                    std::cout << "  Error: Failed to create expanded array" << std::endl;
                                }
                            } else {
                                // Exact size initialization
                                printIndent();
                                std::cout << "  Storing array constant initialization (exact size)" << std::endl;
                                llvm_builder->CreateStore(constArray, alloca);
                            }
                        } else {
                            // Normal full initialization (fallback)
                            printIndent();
                            std::cout << "  Storing array constant initialization" << std::endl;
                            llvm_builder->CreateStore(constArray, alloca);
                        }
                    } else {
                        printIndent();
                        std::cout << "  Error: Array initializer must be constant" << std::endl;
                    }
                }
            } else {
                // Empty initialization: int a = {}; or int a[5] = {};
                printIndent();
                std::cout << "  Creating zero-initialized value" << std::endl;
                llvm::Constant* zeroValue;
                if (varType->isArrayTy()) {
                    zeroValue = createZeroInitializedArray(baseType, dimensions);
                } else {
                    zeroValue = createZeroInitializedArray(baseType, {});
                }
                llvm_builder->CreateStore(zeroValue, alloca);
            }
        }
    }
    // TODO: check scalar

    indent--;
    printIndent();
    std::cout << ") End VarDecl" << std::endl;
    return nullptr;
}

/*
varDef
    : IDENT ( LBRACKET constExp RBRACKET )*
    | IDENT ( LBRACKET constExp RBRACKET )* ASSIGN initVal;
*/
antlrcpp::Any IRGenerator::visitVarDef(SysY2022Parser::VarDefContext *ctx) {
    printIndent();
    std::cout << "VarDef (" << std::endl;
    indent++;

    std::string var_name = ctx->IDENT()->getText();
    printIndent();
    std::cout << "Variable name: " << var_name << std::endl;

    // This method is typically called from visitVarDecl, so the actual
    // variable creation logic is handled there. This method can be used
    // for additional processing if needed.

    indent--;
    printIndent();
    std::cout << ") End VarDef" << std::endl;
    return nullptr;
}

/*
initVal
    : exp
    | LBRACE ( initVal ( COMMA initVal )* )? RBRACE;
*/
antlrcpp::Any IRGenerator::visitInitVal(SysY2022Parser::InitValContext *ctx) {
    printIndent();
    std::cout << "InitVal (" << std::endl;
    indent++;

    llvm::Value* llvm_initVal = nullptr;

    if (ctx->exp()) {
        // Single expression initialization: int a = 5;
        llvm_initVal = visitExp(ctx->exp());
        printIndent();
        std::cout << "Expression initialization" << std::endl;
    } else if (ctx->initVal().size() > 0) {
        // Array initialization: int a[] = {1, 2, 3};
        printIndent();
        std::cout << "Array initialization with " << ctx->initVal().size() << " elements" << std::endl;

        std::vector<llvm::Constant*> elements;
        for (auto init : ctx->initVal()) {
            llvm::Value* element = visitInitVal(init);
            if (auto constElement = llvm::dyn_cast<llvm::Constant>(element)) {
                elements.push_back(constElement);
            } else {
                // TODO: check if initval must be constant
                std::cerr << "Error: Array initializer must be constant" << std::endl;
                elements.push_back(llvm::Constant::getNullValue(llvm::Type::getInt32Ty(*llvm_context)));
            }
        }

        // Create array constant
        if (!elements.empty()) {
            // Determine the element type from the first element
            llvm::Type* elementType = elements[0]->getType();

            // Check if all elements have the same type
            for (auto element : elements) {
                if (element->getType() != elementType) {
                    std::cerr << "Warning: Array elements have different types" << std::endl;
                    break;
                }
            }

            // For now, create array with provided elements only
            // TODO: This needs to be fixed for partial initialization
            llvm::ArrayType* arrayType = llvm::ArrayType::get(elementType, elements.size());
            llvm_initVal = llvm::ConstantArray::get(arrayType, elements);

            printIndent();
            std::cout << "Created array constant with " << elements.size() << " elements (partial init issue)" << std::endl;
        }
    } else {
        // Empty initialization: int a = {};
        printIndent();
        std::cout << "Empty initialization (zero-initialized)" << std::endl;
        llvm_initVal = nullptr; // Will be handled by caller
    }

    indent--;
    printIndent();
    std::cout << ") End InitVal" << std::endl;
    return llvm_initVal;
}

/*
funcFormalParams : funcFormalParam ( COMMA funcFormalParam )*;
*/
antlrcpp::Any IRGenerator::visitFuncFormalParams(SysY2022Parser::FuncFormalParamsContext *ctx) {
    printIndent();
    std::cout << "FuncFormalParams (" << std::endl;
    indent++;

    std::vector<llvm::Type*> paramTypes;
    std::vector<std::string> paramNames;

    for (auto param : ctx->funcFormalParam()) {
        auto paramInfo = visitFuncFormalParam(param);
        try {
            auto info = paramInfo.as<std::pair<llvm::Type*, std::string>>();
            paramTypes.push_back(info.first);
            paramNames.push_back(info.second);
        } catch (...) {
            std::cerr << "Error: Failed to get parameter info" << std::endl;
        }
    }

    indent--;
    printIndent();
    std::cout << ") End FuncFormalParams" << std::endl;

    return std::make_pair(paramTypes, paramNames);
}

antlrcpp::Any IRGenerator::visitFuncFormalParam(SysY2022Parser::FuncFormalParamContext *ctx) {
    printIndent();
    std::cout << "FuncFormalParam (" << std::endl;
    indent++;

    // Get parameter type
    llvm::Type* paramType = get_llvm_type(ctx->bType(), *llvm_context);

    // Get parameter name
    std::string paramName = ctx->IDENT()->getText();

    printIndent();
    std::cout << "Parameter: " << paramName << " of type " << (paramType->isIntegerTy() ? "int" : "float") << std::endl;

    // Check for array parameter (has empty brackets)
    if (ctx->LBRACKET().size() > 0) {
        // Array parameter - convert to pointer type
        paramType = llvm::PointerType::get(paramType, 0);
        printIndent();
        std::cout << "Array parameter (converted to pointer)" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End FuncFormalParam" << std::endl;

    // Return parameter info as a pair
    return std::make_pair(paramType, paramName);
}

/*
stmt
    : lVal ASSIGN exp SEMICOLON
    | exp SEMICOLON
    | block
    | IF LPAREN cond RPAREN stmt ( ELSE stmt )?
    | WHILE LPAREN cond RPAREN stmt
    | BREAK SEMICOLON
    | CONTINUE SEMICOLON
    | RETURN ( exp )? SEMICOLON;
*/
antlrcpp::Any IRGenerator::visitStmt(SysY2022Parser::StmtContext *ctx) {
    llvm::Instruction* llvm_stmt = nullptr;

    // Assignment statement: left_value = right_exp
    if (ctx->ASSIGN()) {
        // lVal ASSIGN exp SEMICOLON
        printIndent();
        std::cout << "Assignment Stmt (" << std::endl;
        indent++;

        if (!(ctx->lVal() && ctx->exp())) {
            std::cerr << "Assignment statement: left_value = right_exp" << std::endl;
            std::cerr << "lVal or exp is nullptr" << std::endl;
            exit(1);
        }

        // Get the left-hand side address (handles both scalars and array elements)
        llvm::Value* var_address = getLValAddress(ctx->lVal());

        // Get the value for the right-hand side
        llvm::Value* r_exp = visitExp(ctx->exp());

        if (!var_address || !r_exp) {
            std::cerr << "Assignment statement: left_value = right_exp" << std::endl;
            std::cerr << "var_address or r_exp is nullptr" << std::endl;
        } else {
            // Correct order: CreateStore(value, address)
            llvm_stmt = llvm_builder->CreateStore(r_exp, var_address);
            printIndent();
            std::cout << "Storing value to lval" << std::endl;
        }

        indent--;
        printIndent();
        std::cout << ") End Assignment" << std::endl;
    } else if (ctx->RETURN()) {
        // Return statement
        printIndent();
        std::cout << "Return Stmt (" << std::endl;
        indent++;

        if (ctx->exp()) {
            // Return with value
            llvm::Value* retVal = visitExp(ctx->exp());
            llvm_stmt = llvm_builder->CreateRet(retVal);
            printIndent();
            std::cout << "Returning value" << std::endl;
        } else {
            // Return void
            llvm_stmt = llvm_builder->CreateRetVoid();
            printIndent();
            std::cout << "Returning void" << std::endl;
        }

        indent--;
        printIndent();
        std::cout << ") End Return" << std::endl;
    } else if (ctx->block()) {
        printIndent();
        std::cout << "Block Stmt (" << std::endl;
        indent++;
        // Block statement
        visitBlock(ctx->block());
        // For block statements, we don't need to return an instruction
        llvm_stmt = nullptr;
        indent--;
        printIndent();
        std::cout << ") End Block" << std::endl;
    } else if (ctx->IF()) {
        // IF (ELSE) statement
        // IF LPAREN cond RPAREN stmt ( ELSE stmt )?
        printIndent();
        std::cout << "If Stmt (" << std::endl;
        indent++;

        // Evaluate condition first
        llvm::Value* cond = visitCond(ctx->cond());

        // Create basic blocks for control flow
        llvm::BasicBlock* then_block = llvm::BasicBlock::Create(*llvm_context, "if.then", current_function);
        llvm::BasicBlock* else_block = nullptr;
        llvm::BasicBlock* merge_block = llvm::BasicBlock::Create(*llvm_context, "if.end", current_function);

        if (ctx->ELSE()) {
            else_block = llvm::BasicBlock::Create(*llvm_context, "if.else", current_function);
        }

        // Create conditional branch
        if (else_block) {
            llvm_stmt = llvm_builder->CreateCondBr(cond, then_block, else_block);
        } else {
            llvm_stmt = llvm_builder->CreateCondBr(cond, then_block, merge_block);
        }

        // Generate then block
        llvm_builder->SetInsertPoint(then_block);
        visitStmt(ctx->stmt(0));
        if (!llvm_builder->GetInsertBlock()->getTerminator()) {
            llvm_builder->CreateBr(merge_block);
        }

        // Generate else block if present
        if (ctx->ELSE()) {
            llvm_builder->SetInsertPoint(else_block);
            visitStmt(ctx->stmt(1));
            if (!llvm_builder->GetInsertBlock()->getTerminator()) {
                llvm_builder->CreateBr(merge_block);
            }
        }

        // Continue with merge block
        llvm_builder->SetInsertPoint(merge_block);

        indent--;
        printIndent();
        std::cout << ") End If" << std::endl;
    } else if (ctx->WHILE()) {
        // WHILE statement
        // WHILE LPAREN cond RPAREN stmt
        printIndent();
        std::cout << "While Stmt (" << std::endl;
        indent++;

        // Create basic blocks for while loop
        llvm::BasicBlock* cond_block = llvm::BasicBlock::Create(*llvm_context, "while.cond", current_function);
        llvm::BasicBlock* body_block = llvm::BasicBlock::Create(*llvm_context, "while.body", current_function);
        llvm::BasicBlock* end_block = llvm::BasicBlock::Create(*llvm_context, "while.end", current_function);

        // Set up loop context for break/continue
        // break -> end_block, continue -> cond_block (re-evaluate condition)
        pushLoop(end_block, cond_block, "while");

        // Jump to condition block
        llvm_builder->CreateBr(cond_block);

        // Generate condition block
        llvm_builder->SetInsertPoint(cond_block);
        llvm::Value* cond = visitCond(ctx->cond());
        llvm_builder->CreateCondBr(cond, body_block, end_block);

        // Generate body block
        llvm_builder->SetInsertPoint(body_block);
        visitStmt(ctx->stmt(0));
        if (!llvm_builder->GetInsertBlock()->getTerminator()) {
            llvm_builder->CreateBr(cond_block); // Loop back to condition
        }

        // Pop loop context
        popLoop();

        // Continue with end block
        llvm_builder->SetInsertPoint(end_block);

        indent--;
        printIndent();
        std::cout << ") End While" << std::endl;
    } else if (ctx->BREAK()) {
        // BREAK statement
        // BREAK SEMICOLON
        printIndent();
        std::cout << "Break Stmt" << std::endl;

        llvm::BasicBlock* break_target = getCurrentBreakTarget();
        if (break_target) {
            llvm_stmt = llvm_builder->CreateBr(break_target);
            printIndent();
            std::cout << "Breaking to: " << break_target->getName().str() << std::endl;
        } else {
            std::cerr << "Error: break statement not inside a loop" << std::endl;
            // Create a dummy branch to avoid crashes - this should be caught by semantic analysis
            llvm::BasicBlock* dummy_block = llvm::BasicBlock::Create(*llvm_context, "break.error", current_function);
            llvm_stmt = llvm_builder->CreateBr(dummy_block);
        }

    } else if (ctx->CONTINUE()) {
        // CONTINUE statement
        // CONTINUE SEMICOLON
        printIndent();
        std::cout << "Continue Stmt" << std::endl;

        llvm::BasicBlock* continue_target = getCurrentContinueTarget();
        if (continue_target) {
            llvm_stmt = llvm_builder->CreateBr(continue_target);
            printIndent();
            std::cout << "Continuing to: " << continue_target->getName().str() << std::endl;
        } else {
            std::cerr << "Error: continue statement not inside a loop" << std::endl;
            // Create a dummy branch to avoid crashes - this should be caught by semantic analysis
            llvm::BasicBlock* dummy_block = llvm::BasicBlock::Create(*llvm_context, "continue.error", current_function);
            llvm_stmt = llvm_builder->CreateBr(dummy_block);
        }
    } else if (ctx->exp()) {
        // Expression statement
        printIndent();
        std::cout << "Expression Stmt (" << std::endl;
        indent++;
        llvm::Value* exp_value = visitExp(ctx->exp());
        // For expression statements, we don't need to store the result
        // Just evaluate the expression for side effects (like function calls)
        llvm_stmt = nullptr;
        indent--;
        printIndent();
        std::cout << ") End Expression" << std::endl;
    } else {
        std::cerr << "Unknown statement:" << ctx->toString() << std::endl;
    }

    return llvm_stmt;
}

/*
cond: lOrExp;
*/
// TODO: check convert logic
antlrcpp::Any IRGenerator::visitCond(SysY2022Parser::CondContext *ctx) {
    printIndent();
    std::cout << "Cond (" << std::endl;
    indent++;

    llvm::Value* llvm_cond = nullptr;

    if (ctx->lOrExp()) {
        llvm_cond = visitLOrExp(ctx->lOrExp());

        // Convert to boolean if needed
        if (llvm_cond && !llvm_cond->getType()->isIntegerTy(1)) {
            // Compare with zero to get boolean result
            if (llvm_cond->getType()->isIntegerTy()) {
                llvm::Value* zero = llvm::ConstantInt::get(llvm_cond->getType(), 0);
                llvm_cond = llvm_builder->CreateICmpNE(llvm_cond, zero, "condtmp");
            } else if (llvm_cond->getType()->isFloatTy()) {
                llvm::Value* zero = llvm::ConstantFP::get(llvm_cond->getType(), 0.0);
                llvm_cond = llvm_builder->CreateFCmpONE(llvm_cond, zero, "condtmp");
            }
        }
    } else {
        std::cerr << "Unknown cond format" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End Cond" << std::endl;

    return llvm_cond;
}

/*
funcRealParams : exp ( COMMA exp )*;
*/
antlrcpp::Any IRGenerator::visitFuncRealParams(SysY2022Parser::FuncRealParamsContext *ctx) {
    printIndent();
    std::cout << "FuncRealParams (" << std::endl;
    indent++;

    auto args = std::make_shared<std::vector<llvm::Value*>>();

    for (auto exp : ctx->exp()) {
        llvm::Value* arg = visitExp(exp);
        if (arg) {
            args->push_back(arg);
            // printIndent();
            // std::cout << "Argument added" << std::endl;
        } else {
            std::cerr << "Failed to get argument" << std::endl;
        }
    }

    indent--;
    printIndent();
    std::cout << ") End FuncRealParams" << std::endl;

    return args;
}

/*
relExp
    : addExp
    | relExp ( LT | GT | LE | GE ) addExp ;
*/
antlrcpp::Any IRGenerator::visitRelExp(SysY2022Parser::RelExpContext *ctx) {
    printIndent();
    std::cout << "RelExp (" << std::endl;
    indent++;

    llvm::Value* llvm_relExp = nullptr;

    if (ctx->relExp()) {
        // relExp ( LT | GT | LE | GE ) addExp

        llvm::Value* left = visitRelExp(ctx->relExp());
        llvm::Value* right = visitAddExp(ctx->addExp());

        if (left && right) {
            // Handle type conversion for mixed type comparisons
            if (left->getType() != right->getType()) {
                if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                    // Convert to float if either operand is float
                    if (left->getType()->isIntegerTy()) {
                        left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                    }
                    if (right->getType()->isIntegerTy()) {
                        right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                    }
                }
            }

            // Generate appropriate comparison based on type
            if (left->getType()->isFloatTy()) {
                // Float comparisons
                if (ctx->LT()) {
                    llvm_relExp = llvm_builder->CreateFCmpOLT(left, right, "fcmplt");
                } else if (ctx->GT()) {
                    llvm_relExp = llvm_builder->CreateFCmpOGT(left, right, "fcmpgt");
                } else if (ctx->LE()) {
                    llvm_relExp = llvm_builder->CreateFCmpOLE(left, right, "fcmple");
                } else if (ctx->GE()) {
                    llvm_relExp = llvm_builder->CreateFCmpOGE(left, right, "fcmpge");
                }
            } else {
                // Integer comparisons
                if (ctx->LT()) {
                    llvm_relExp = llvm_builder->CreateICmpSLT(left, right, "icmplt");
                } else if (ctx->GT()) {
                    llvm_relExp = llvm_builder->CreateICmpSGT(left, right, "icmpgt");
                } else if (ctx->LE()) {
                    llvm_relExp = llvm_builder->CreateICmpSLE(left, right, "icmple");
                } else if (ctx->GE()) {
                    llvm_relExp = llvm_builder->CreateICmpSGE(left, right, "icmpge");
                }
            }
        }
    } else if (ctx->addExp()) {
        // Just addExp

        llvm_relExp = visitAddExp(ctx->addExp());
    } else {
        std::cerr << "Unknown expression in RelExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End RelExp" << std::endl;



    return llvm_relExp;
}

/*
eqExp
    : relExp
    | eqExp ( EQ | NE ) relExp;
*/
antlrcpp::Any IRGenerator::visitEqExp(SysY2022Parser::EqExpContext *ctx) {
    printIndent();
    std::cout << "EqExp (" << std::endl;
    indent++;

    llvm::Value* llvm_eqExp = nullptr;

    if (ctx->eqExp()) {
        // eqExp ( EQ | NE ) relExp
        llvm::Value* left = visitEqExp(ctx->eqExp());
        llvm::Value* right = visitRelExp(ctx->relExp());

        if (left && right) {
            // Handle type conversion for mixed type comparisons
            if (left->getType() != right->getType()) {
                if (left->getType()->isFloatTy() || right->getType()->isFloatTy()) {
                    // Convert to float if either operand is float
                    if (left->getType()->isIntegerTy()) {
                        left = llvm_builder->CreateSIToFP(left, llvm::Type::getFloatTy(*llvm_context), "int2float");
                    }
                    if (right->getType()->isIntegerTy()) {
                        right = llvm_builder->CreateSIToFP(right, llvm::Type::getFloatTy(*llvm_context), "int2float");
                    }
                }
            }

            // Generate appropriate comparison based on type
            if (left->getType()->isFloatTy()) {
                // Float comparisons
                if (ctx->EQ()) {
                    llvm_eqExp = llvm_builder->CreateFCmpOEQ(left, right, "fcmpeq");
                } else if (ctx->NE()) {
                    llvm_eqExp = llvm_builder->CreateFCmpONE(left, right, "fcmpne");
                }
            } else {
                // Integer comparisons
                if (ctx->EQ()) {
                    llvm_eqExp = llvm_builder->CreateICmpEQ(left, right, "icmpeq");
                } else if (ctx->NE()) {
                    llvm_eqExp = llvm_builder->CreateICmpNE(left, right, "icmpne");
                }
            }
        }
    } else if (ctx->relExp()) {
        // Just relExp
        llvm_eqExp = visitRelExp(ctx->relExp());
    } else {
        std::cerr << "Unknown expression in EqExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End EqExp" << std::endl;
    return llvm_eqExp;
}

/*
lAndExp
    : eqExp
    | lAndExp AND eqExp;
*/
antlrcpp::Any IRGenerator::visitLAndExp(SysY2022Parser::LAndExpContext *ctx) {
    printIndent();
    std::cout << "LAndExp (" << std::endl;
    indent++;

    llvm::Value* llvm_landExp = nullptr;

    // TODO: check logic for mixed type comparison
    if (ctx->lAndExp()) {
        // lAndExp AND eqExp
        llvm::Value* left = visitLAndExp(ctx->lAndExp());
        llvm::Value* right = visitEqExp(ctx->eqExp());

        if (left && right) {
            // Convert to boolean if needed
            if (!left->getType()->isIntegerTy(1)) {
                if (left->getType()->isFloatTy()) {
                    // Compare float with 0.0
                    llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                    left = llvm_builder->CreateFCmpONE(left, zero, "ftobool");
                } else if (left->getType()->isIntegerTy()) {
                    // Compare integer with 0
                    llvm::Value* zero = llvm::ConstantInt::get(left->getType(), 0);
                    left = llvm_builder->CreateICmpNE(left, zero, "itobool");
                }
            }
            if (!right->getType()->isIntegerTy(1)) {
                if (right->getType()->isFloatTy()) {
                    // Compare float with 0.0
                    llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                    right = llvm_builder->CreateFCmpONE(right, zero, "ftobool");
                } else if (right->getType()->isIntegerTy()) {
                    // Compare integer with 0
                    llvm::Value* zero = llvm::ConstantInt::get(right->getType(), 0);
                    right = llvm_builder->CreateICmpNE(right, zero, "itobool");
                }
            }

            llvm_landExp = llvm_builder->CreateAnd(left, right, "landtmp");
        }
    } else if (ctx->eqExp()) {
        // Just eqExp
        llvm_landExp = visitEqExp(ctx->eqExp());
    } else {
        std::cerr << "Unknown expression in LAndExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End LAndExp" << std::endl;
    return llvm_landExp;
}

/*
lOrExp
    : lAndExp
    | lOrExp OR lAndExp;
*/
antlrcpp::Any IRGenerator::visitLOrExp(SysY2022Parser::LOrExpContext *ctx) {
    printIndent();
    std::cout << "LOrExp (" << std::endl;
    indent++;

    llvm::Value* llvm_lorExp = nullptr;

    if (ctx->lOrExp()) {
        // lOrExp OR lAndExp
        llvm::Value* left = visitLOrExp(ctx->lOrExp());
        llvm::Value* right = visitLAndExp(ctx->lAndExp());

        if (left && right) {
            // Convert to boolean if needed
            if (!left->getType()->isIntegerTy(1)) {
                if (left->getType()->isFloatTy()) {
                    // Compare float with 0.0
                    llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                    left = llvm_builder->CreateFCmpONE(left, zero, "ftobool");
                } else if (left->getType()->isIntegerTy()) {
                    // Compare integer with 0
                    llvm::Value* zero = llvm::ConstantInt::get(left->getType(), 0);
                    left = llvm_builder->CreateICmpNE(left, zero, "itobool");
                }
            }
            if (!right->getType()->isIntegerTy(1)) {
                if (right->getType()->isFloatTy()) {
                    // Compare float with 0.0
                    llvm::Value* zero = llvm::ConstantFP::get(llvm::Type::getFloatTy(*llvm_context), 0.0);
                    right = llvm_builder->CreateFCmpONE(right, zero, "ftobool");
                } else if (right->getType()->isIntegerTy()) {
                    // Compare integer with 0
                    llvm::Value* zero = llvm::ConstantInt::get(right->getType(), 0);
                    right = llvm_builder->CreateICmpNE(right, zero, "itobool");
                }
            }

            llvm_lorExp = llvm_builder->CreateOr(left, right, "lortmp");
        }
    } else if (ctx->lAndExp()) {
        // Just lAndExp
        llvm_lorExp = visitLAndExp(ctx->lAndExp());
    } else {
        std::cerr << "Unknown expression in LOrExp" << std::endl;
    }

    indent--;
    printIndent();
    std::cout << ") End LOrExp" << std::endl;
    return llvm_lorExp;
}

// Helper method to get the address of an lvalue (for assignments)
llvm::Value* IRGenerator::getLValAddress(SysY2022Parser::LValContext *ctx) {
    std::string var_name = ctx->IDENT()->getText();

    // Check if variable exists in symbol table
    if (named_values.find(var_name) == named_values.end()) {
        std::cerr << "Undefined variable in assignment: " << var_name << std::endl;
        return nullptr;
    }

    llvm::Value* var = named_values[var_name][0];

    // Handle array indexing if present
    if (!ctx->exp().empty()) {
        // Array access: var[index1][index2]...
        printIndent();
        std::cout << "Getting address for array element: " << var_name << "[";

        // Start with the base pointer
        llvm::Value* ptr = var;

        // Build all indices for multi-dimensional array access
        std::vector<llvm::Value*> indices;
        indices.push_back(llvm::ConstantInt::get(llvm::Type::getInt32Ty(*llvm_context), 0)); // Base index

        for (size_t i = 0; i < ctx->exp().size(); i++) {
            llvm::Value* index = visitExp(ctx->exp(i));
            indices.push_back(index);
            if (i > 0) std::cout << "][";
            std::cout << "index";
        }

        // Single GEP operation with all indices
        llvm::Type* baseType = llvm::dyn_cast<llvm::AllocaInst>(ptr)->getAllocatedType();
        ptr = llvm_builder->CreateGEP(baseType, ptr, indices, "arrayidx");
        std::cout << "]" << std::endl;

        return ptr; // Return the address, not the loaded value
    } else {
        // Scalar variable - return the alloca directly
        return var;
    }
}
