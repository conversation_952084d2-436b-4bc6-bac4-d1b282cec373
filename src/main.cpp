#include <iostream>

#include "antlr4-runtime.h"
#include "SysY2022Lexer.h"
#include "SysY2022Parser.h"
#include "Backend.hpp"

using namespace antlr4;

int main(int argc, const char* argv[]) {
    std::ifstream stream;
    stream.open(argv[1]);
    ANTLRInputStream input(stream);
    //ANTLRInputStream input(std::cin);

    // Lexer
    SysY2022Lexer lexer(&input);
    CommonTokenStream tokens(&lexer);

    tokens.fill();
   
    for (auto token : tokens.getTokens()) {
  
        //简单粗暴的输出token信息并不符合题目要求
        std::cout << token->toString() << std::endl;
    }

    /* 语法分析
    sysyparser parser(&tokens);
    tree::parsetree* tree = parser.compunit();

    std::cout << tree->tostringtree(&parser) << std::endl << std::endl;
    */
    SysY2022Parser parser(&tokens);
    tree::ParseTree* tree = parser.program();

    std::cout << tree->toStringTree(&parser) << std::endl << std::endl;


    // std::cout << "=== AST Structure ===" << std::endl;
    // ASTVisitor visitor;
    // visitor.visit(tree);

    // // Print unoptimized LLVM IR
    // std::cout << "\n=== Unoptimized LLVM IR ===" << std::endl;
    // visitor.print_llvm_ir();

    // // Run optimization passes (including mem2reg)
    // visitor.optimize_ir();

    // // Print optimized LLVM IR
    // std::cout << "\n=== Optimized LLVM IR (after mem2reg) ===" << std::endl;
    // visitor.print_llvm_ir();

    // Demonstrate new modular backend
    std::cout << "\n=== New Modular Backend Demo ===" << std::endl;

    // Create backend with O2 optimization for ARM target
    Backend modular_backend("SysY2022", Optimizer::O2, true);

    // Full compilation pipeline: AST -> IR -> Optimization -> Assembly
    modular_backend.compile(tree, "output.s", "assembly");

    // Also generate executable
    std::cout << "\n=== Generating Executable ===" << std::endl;
    modular_backend.compile(tree, "output", "executable");

    std::cout << "\n=== Modular Backend LLVM IR ===" << std::endl;
    modular_backend.printLLVMIR();

    return 0;
}    